{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7e3951c9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta \n", "import os"]}, {"cell_type": "code", "execution_count": 20, "id": "9f1cafe5", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "标识串及含义：\n", "B  限价买入                S  限价卖出\n", "1B 市价买入               1S 市价卖出\n", "UB 本方最优市价买入  US 本方最优市价卖出\n", "BC 撤买入单               SC 撤卖出单\n", "\"\"\"\n", "# 成交类型\n", "ORDER_TYPES = [\"B\", \"S\", \"BC\", \"SC\", \"1B\", \"1S\", \"UB\", \"US\"]\n", "B, S, BC, SC, _1B, _1S, UB, US = range(len(ORDER_TYPES))\n", "\n", "# 基础路径\n", "root = r\"D:\\通达信录制数据\\realtime_data\""]}, {"cell_type": "code", "execution_count": 27, "id": "05758daf", "metadata": {}, "outputs": [], "source": ["# 设置项\n", "date = '20230901'"]}, {"cell_type": "code", "execution_count": 28, "id": "d079d726", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>09:15:09</td>\n", "      <td>26</td>\n", "      <td>NaN</td>\n", "      <td>1.069</td>\n", "      <td>111</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>09:15:09</td>\n", "      <td>27</td>\n", "      <td>NaN</td>\n", "      <td>1.073</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>09:15:09</td>\n", "      <td>28</td>\n", "      <td>NaN</td>\n", "      <td>1.074</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>09:15:09</td>\n", "      <td>29</td>\n", "      <td>NaN</td>\n", "      <td>1.075</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>09:15:09</td>\n", "      <td>30</td>\n", "      <td>NaN</td>\n", "      <td>1.076</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45782</th>\n", "      <td>14:59:57</td>\n", "      <td>3</td>\n", "      <td>1h</td>\n", "      <td>1.059</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45783</th>\n", "      <td>14:59:57</td>\n", "      <td>4</td>\n", "      <td>1h</td>\n", "      <td>1.060</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45784</th>\n", "      <td>14:59:57</td>\n", "      <td>5</td>\n", "      <td>1h</td>\n", "      <td>1.061</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45785</th>\n", "      <td>14:59:58</td>\n", "      <td>1</td>\n", "      <td>16m</td>\n", "      <td>1.065</td>\n", "      <td>20</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45786</th>\n", "      <td>14:59:58</td>\n", "      <td>2</td>\n", "      <td>1h</td>\n", "      <td>1.068</td>\n", "      <td>820</td>\n", "      <td>SC</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>45787 rows × 6 columns</p>\n", "</div>"], "text/plain": ["             时间  序号 在委时间     价格    手  类型\n", "0      09:15:09  26  NaN  1.069  111   S\n", "1      09:15:09  27  NaN  1.073   10   S\n", "2      09:15:09  28  NaN  1.074   10   S\n", "3      09:15:09  29  NaN  1.075   10   S\n", "4      09:15:09  30  NaN  1.076   10   S\n", "...         ...  ..  ...    ...  ...  ..\n", "45782  14:59:57   3   1h  1.059    1  BC\n", "45783  14:59:57   4   1h  1.060    1  BC\n", "45784  14:59:57   5   1h  1.061    1  BC\n", "45785  14:59:58   1  16m  1.065   20  BC\n", "45786  14:59:58   2   1h  1.068  820  SC\n", "\n", "[45787 rows x 6 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取数据\n", "data_path = os.path.join(root + '/ago/data', date) if date<\"20230906\" else os.path.join(root, date) + \"/data\"\n", "file = os.path.join(data_path, \"逐笔委托.csv\")\n", "data = pd.read_csv(file, encoding=\"gbk\", dtype=str)\n", "data"]}, {"cell_type": "code", "execution_count": 29, "id": "a764bb9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["0        False\n", "1        False\n", "2        False\n", "3        False\n", "4        False\n", "         ...  \n", "45782    False\n", "45783    False\n", "45784    False\n", "45785    False\n", "45786    False\n", "Name: 价格, Length: 45787, dtype: bool"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["error = data[\"价格\"].apply(lambda x: False if \".\" in x else True)\n", "error"]}, {"cell_type": "code", "execution_count": 30, "id": "12d97b9b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>26</td>\n", "      <td>NaN</td>\n", "      <td>1.069</td>\n", "      <td>111</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>27</td>\n", "      <td>NaN</td>\n", "      <td>1.073</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>28</td>\n", "      <td>NaN</td>\n", "      <td>1.074</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>29</td>\n", "      <td>NaN</td>\n", "      <td>1.075</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>30</td>\n", "      <td>NaN</td>\n", "      <td>1.076</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45782</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>3</td>\n", "      <td>1h</td>\n", "      <td>1.059</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45783</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>4</td>\n", "      <td>1h</td>\n", "      <td>1.060</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45784</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>5</td>\n", "      <td>1h</td>\n", "      <td>1.061</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45785</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>1</td>\n", "      <td>16m</td>\n", "      <td>1.065</td>\n", "      <td>20</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45786</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>2</td>\n", "      <td>1h</td>\n", "      <td>1.068</td>\n", "      <td>820</td>\n", "      <td>SC</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>45787 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                       时间  序号 在委时间     价格    手  类型\n", "0     2023-09-01 09:15:09  26  NaN  1.069  111   S\n", "1     2023-09-01 09:15:09  27  NaN  1.073   10   S\n", "2     2023-09-01 09:15:09  28  NaN  1.074   10   S\n", "3     2023-09-01 09:15:09  29  NaN  1.075   10   S\n", "4     2023-09-01 09:15:09  30  NaN  1.076   10   S\n", "...                   ...  ..  ...    ...  ...  ..\n", "45782 2023-09-01 14:59:57   3   1h  1.059    1  BC\n", "45783 2023-09-01 14:59:57   4   1h  1.060    1  BC\n", "45784 2023-09-01 14:59:57   5   1h  1.061    1  BC\n", "45785 2023-09-01 14:59:58   1  16m  1.065   20  BC\n", "45786 2023-09-01 14:59:58   2   1h  1.068  820  SC\n", "\n", "[45787 rows x 6 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"价格\"] = data[\"价格\"].astype(float)\n", "\n", "# 涨跌停，一买卖价格处理\n", "data[\"价格\"].fillna(-1, inplace=True)\n", "\n", "# 格式化\n", "# 时间\n", "date = date + \" \"\n", "data[\"时间\"] = data[\"时间\"].apply(\n", "    lambda x: \"09:30:01\" if x == \"09:30:00\" else x\n", ")  # 09:30:00 -> 09:30:01\n", "data[\"时间\"] = data[\"时间\"].apply(\n", "    lambda x: \"13:00:01\" if x == \"13:00:00\" else x\n", ")  # 13:00:00 -> 13:00:01\n", "data[\"时间\"] = data[\"时间\"].apply(lambda x: date + x)\n", "data[\"时间\"] = pd.to_datetime(data[\"时间\"])\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 31, "id": "cfd1009b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>1.069</td>\n", "      <td>111</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>1.073</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>1.074</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>1.075</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>1.076</td>\n", "      <td>10</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45782</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>3</td>\n", "      <td>3600</td>\n", "      <td>1.059</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45783</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>4</td>\n", "      <td>3600</td>\n", "      <td>1.060</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45784</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>5</td>\n", "      <td>3600</td>\n", "      <td>1.061</td>\n", "      <td>1</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45785</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>1</td>\n", "      <td>960</td>\n", "      <td>1.065</td>\n", "      <td>20</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45786</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>2</td>\n", "      <td>3600</td>\n", "      <td>1.068</td>\n", "      <td>820</td>\n", "      <td>SC</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>45787 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                       时间  序号  在委时间     价格    手  类型\n", "0     2023-09-01 09:15:09  26     0  1.069  111   S\n", "1     2023-09-01 09:15:09  27     0  1.073   10   S\n", "2     2023-09-01 09:15:09  28     0  1.074   10   S\n", "3     2023-09-01 09:15:09  29     0  1.075   10   S\n", "4     2023-09-01 09:15:09  30     0  1.076   10   S\n", "...                   ...  ..   ...    ...  ...  ..\n", "45782 2023-09-01 14:59:57   3  3600  1.059    1  BC\n", "45783 2023-09-01 14:59:57   4  3600  1.060    1  BC\n", "45784 2023-09-01 14:59:57   5  3600  1.061    1  BC\n", "45785 2023-09-01 14:59:58   1   960  1.065   20  BC\n", "45786 2023-09-01 14:59:58   2  3600  1.068  820  SC\n", "\n", "[45787 rows x 6 columns]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# 在委时间\n", "data.loc[data[\"在委时间\"].isna(), \"在委时间\"] = \"0s\"\n", "data[\"在委时间\"] = data[\"在委时间\"].apply(\n", "    lambda x: int(x[:-1])\n", "    if \"s\" in x\n", "    else int(x[:-1]) * 60\n", "    if \"m\" in x\n", "    else int(x[:-1]) * 60 * 60\n", ")\n", "data"]}, {"cell_type": "code", "execution_count": 32, "id": "ab3de14e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>1.069</td>\n", "      <td>111</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>1.073</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>1.074</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>1.075</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:15:09</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>1.076</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45782</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>3</td>\n", "      <td>3600</td>\n", "      <td>1.059</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45783</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>4</td>\n", "      <td>3600</td>\n", "      <td>1.060</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45784</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>5</td>\n", "      <td>3600</td>\n", "      <td>1.061</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45785</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>1</td>\n", "      <td>960</td>\n", "      <td>1.065</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45786</th>\n", "      <td>2023-09-01 14:59:58</td>\n", "      <td>2</td>\n", "      <td>3600</td>\n", "      <td>1.068</td>\n", "      <td>820</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>45787 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                       时间  序号  在委时间     价格    手  类型\n", "0     2023-09-01 09:15:09  26     0  1.069  111   1\n", "1     2023-09-01 09:15:09  27     0  1.073   10   1\n", "2     2023-09-01 09:15:09  28     0  1.074   10   1\n", "3     2023-09-01 09:15:09  29     0  1.075   10   1\n", "4     2023-09-01 09:15:09  30     0  1.076   10   1\n", "...                   ...  ..   ...    ...  ...  ..\n", "45782 2023-09-01 14:59:57   3  3600  1.059    1   2\n", "45783 2023-09-01 14:59:57   4  3600  1.060    1   2\n", "45784 2023-09-01 14:59:57   5  3600  1.061    1   2\n", "45785 2023-09-01 14:59:58   1   960  1.065   20   2\n", "45786 2023-09-01 14:59:58   2  3600  1.068  820   3\n", "\n", "[45787 rows x 6 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# 手 卖出会存在小数\n", "data[\"手\"] = data[\"手\"].astype(float).astype(int)\n", "\n", "# 类型\n", "data[\"类型\"] = data[\"类型\"].apply(lambda x: ORDER_TYPES.index(x))\n", "    \n", "data"]}, {"cell_type": "code", "execution_count": 33, "id": "52efeb64", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:15:09</th>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>1.069</td>\n", "      <td>111</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:15:09</th>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>1.073</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:15:09</th>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>1.074</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:15:09</th>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>1.075</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:15:09</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>1.076</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>3</td>\n", "      <td>3600</td>\n", "      <td>1.059</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>4</td>\n", "      <td>3600</td>\n", "      <td>1.060</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>5</td>\n", "      <td>3600</td>\n", "      <td>1.061</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:58</th>\n", "      <td>1</td>\n", "      <td>960</td>\n", "      <td>1.065</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:58</th>\n", "      <td>2</td>\n", "      <td>3600</td>\n", "      <td>1.068</td>\n", "      <td>820</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>45787 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                     序号  在委时间     价格    手  类型\n", "时间                                           \n", "2023-09-01 09:15:09  26     0  1.069  111   1\n", "2023-09-01 09:15:09  27     0  1.073   10   1\n", "2023-09-01 09:15:09  28     0  1.074   10   1\n", "2023-09-01 09:15:09  29     0  1.075   10   1\n", "2023-09-01 09:15:09  30     0  1.076   10   1\n", "...                  ..   ...    ...  ...  ..\n", "2023-09-01 14:59:57   3  3600  1.059    1   2\n", "2023-09-01 14:59:57   4  3600  1.060    1   2\n", "2023-09-01 14:59:57   5  3600  1.061    1   2\n", "2023-09-01 14:59:58   1   960  1.065   20   2\n", "2023-09-01 14:59:58   2  3600  1.068  820   3\n", "\n", "[45787 rows x 5 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["data = data.set_index(\"时间\")\n", "data"]}, {"cell_type": "code", "execution_count": 34, "id": "07e87385", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>区间委卖1</th>\n", "      <th>区间委卖优</th>\n", "      <th>区间委撤</th>\n", "      <th>区间委撤买</th>\n", "      <th>区间委撤卖</th>\n", "      <th>...</th>\n", "      <th>区间卖手</th>\n", "      <th>区间卖手波动率</th>\n", "      <th>区间卖手1</th>\n", "      <th>区间卖手优</th>\n", "      <th>区间撤手</th>\n", "      <th>区间撤手买</th>\n", "      <th>区间撤手卖</th>\n", "      <th>区间撤额</th>\n", "      <th>区间撤额买</th>\n", "      <th>区间撤额卖</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:00</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>47547</td>\n", "      <td>10007</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27278</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10262</td>\n", "      <td>8971</td>\n", "      <td>1291</td>\n", "      <td>...</td>\n", "      <td>12817702.0</td>\n", "      <td>996.217702</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3178228.0</td>\n", "      <td>1990820.0</td>\n", "      <td>1187408.0</td>\n", "      <td>3368307.048</td>\n", "      <td>2080638.461</td>\n", "      <td>1287668.587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>12843</td>\n", "      <td>3070</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8810</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>963</td>\n", "      <td>908</td>\n", "      <td>55</td>\n", "      <td>...</td>\n", "      <td>3889720.0</td>\n", "      <td>881.730665</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>319606.0</td>\n", "      <td>290291.0</td>\n", "      <td>29315.0</td>\n", "      <td>333170.276</td>\n", "      <td>299076.931</td>\n", "      <td>34093.345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>9619</td>\n", "      <td>1566</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6437</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1616</td>\n", "      <td>1032</td>\n", "      <td>584</td>\n", "      <td>...</td>\n", "      <td>3325980.0</td>\n", "      <td>1197.902032</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1192319.0</td>\n", "      <td>778521.0</td>\n", "      <td>413798.0</td>\n", "      <td>1267137.976</td>\n", "      <td>812557.510</td>\n", "      <td>454580.466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>5022</td>\n", "      <td>1023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3241</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>758</td>\n", "      <td>596</td>\n", "      <td>162</td>\n", "      <td>...</td>\n", "      <td>782840.0</td>\n", "      <td>327.411509</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>65202.0</td>\n", "      <td>41001.0</td>\n", "      <td>24201.0</td>\n", "      <td>69286.150</td>\n", "      <td>43507.683</td>\n", "      <td>25778.467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>670.0</td>\n", "      <td>89.716726</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>135.0</td>\n", "      <td>45.0</td>\n", "      <td>90.0</td>\n", "      <td>144.135</td>\n", "      <td>47.835</td>\n", "      <td>96.300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2277.0</td>\n", "      <td>2277.0</td>\n", "      <td>0.0</td>\n", "      <td>2425.038</td>\n", "      <td>2425.038</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>50</td>\n", "      <td>40</td>\n", "      <td>10</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11520.0</td>\n", "      <td>6470.0</td>\n", "      <td>5050.0</td>\n", "      <td>12302.470</td>\n", "      <td>6884.120</td>\n", "      <td>5418.350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1680.0</td>\n", "      <td>40.0</td>\n", "      <td>1640.0</td>\n", "      <td>1794.120</td>\n", "      <td>42.600</td>\n", "      <td>1751.520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:03</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6602 rows × 26 columns</p>\n", "</div>"], "text/plain": ["                      区间委托   区间委买  区间委买1  区间委买优   区间委卖  区间委卖1  区间委卖优   区间委撤  \\\n", "时间                                                                            \n", "2023-09-01 09:30:00      1      0      0      0      0      0      0      0   \n", "2023-09-01 09:30:03  47547  10007      0      0  27278      0      0  10262   \n", "2023-09-01 09:30:06  12843   3070      0      0   8810      0      0    963   \n", "2023-09-01 09:30:09   9619   1566      0      0   6437      0      0   1616   \n", "2023-09-01 09:30:12   5022   1023      0      0   3241      0      0    758   \n", "...                    ...    ...    ...    ...    ...    ...    ...    ...   \n", "2023-09-01 14:59:51     11      0      0      0      7      0      0      4   \n", "2023-09-01 14:59:54      6      2      0      0      0      0      0      4   \n", "2023-09-01 14:59:57     50      0      0      0      0      0      0     50   \n", "2023-09-01 15:00:00      4      0      0      0      0      0      0      4   \n", "2023-09-01 15:00:03      1      0      0      0      0      0      0      0   \n", "\n", "                     区间委撤买  区间委撤卖  ...        区间卖手      区间卖手波动率  区间卖手1  区间卖手优  \\\n", "时间                                 ...                                          \n", "2023-09-01 09:30:00      0      0  ...         0.0          NaN    0.0    0.0   \n", "2023-09-01 09:30:03   8971   1291  ...  12817702.0   996.217702    0.0    0.0   \n", "2023-09-01 09:30:06    908     55  ...   3889720.0   881.730665    0.0    0.0   \n", "2023-09-01 09:30:09   1032    584  ...   3325980.0  1197.902032    0.0    0.0   \n", "2023-09-01 09:30:12    596    162  ...    782840.0   327.411509    0.0    0.0   \n", "...                    ...    ...  ...         ...          ...    ...    ...   \n", "2023-09-01 14:59:51      3      1  ...       670.0    89.716726    0.0    0.0   \n", "2023-09-01 14:59:54      4      0  ...         0.0     0.000000    0.0    0.0   \n", "2023-09-01 14:59:57     40     10  ...         0.0     0.000000    0.0    0.0   \n", "2023-09-01 15:00:00      2      2  ...         0.0     0.000000    0.0    0.0   \n", "2023-09-01 15:00:03      0      0  ...         0.0          NaN    0.0    0.0   \n", "\n", "                          区间撤手      区间撤手买      区间撤手卖         区间撤额  \\\n", "时间                                                                  \n", "2023-09-01 09:30:00        0.0        0.0        0.0        0.000   \n", "2023-09-01 09:30:03  3178228.0  1990820.0  1187408.0  3368307.048   \n", "2023-09-01 09:30:06   319606.0   290291.0    29315.0   333170.276   \n", "2023-09-01 09:30:09  1192319.0   778521.0   413798.0  1267137.976   \n", "2023-09-01 09:30:12    65202.0    41001.0    24201.0    69286.150   \n", "...                        ...        ...        ...          ...   \n", "2023-09-01 14:59:51      135.0       45.0       90.0      144.135   \n", "2023-09-01 14:59:54     2277.0     2277.0        0.0     2425.038   \n", "2023-09-01 14:59:57    11520.0     6470.0     5050.0    12302.470   \n", "2023-09-01 15:00:00     1680.0       40.0     1640.0     1794.120   \n", "2023-09-01 15:00:03        0.0        0.0        0.0        0.000   \n", "\n", "                           区间撤额买        区间撤额卖  \n", "时间                                             \n", "2023-09-01 09:30:00        0.000        0.000  \n", "2023-09-01 09:30:03  2080638.461  1287668.587  \n", "2023-09-01 09:30:06   299076.931    34093.345  \n", "2023-09-01 09:30:09   812557.510   454580.466  \n", "2023-09-01 09:30:12    43507.683    25778.467  \n", "...                          ...          ...  \n", "2023-09-01 14:59:51       47.835       96.300  \n", "2023-09-01 14:59:54     2425.038        0.000  \n", "2023-09-01 14:59:57     6884.120     5418.350  \n", "2023-09-01 15:00:00       42.600     1751.520  \n", "2023-09-01 15:00:03        0.000        0.000  \n", "\n", "[6602 rows x 26 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# 切片交易时间内的数据\n", "data = data.loc[data.between_time(\"09:30:00\", \"15:00:00\").index, :]\n", "\n", "# if len(data) == 0:\n", "#     return None\n", "\n", "# 添加空行，确保每个切片都有重采样数据\n", "date_str = str(data.index[0])[:11]\n", "data.loc[pd.to_datetime(date_str + \"09:29:59\")] = np.nan\n", "data.loc[pd.to_datetime(date_str + \"15:00:01\")] = np.nan\n", "\n", "# 订单数量\n", "data[\"区间委托\"] = 1\n", "data[\"区间委买\"] = data[\"类型\"].apply(lambda x: 1 if x in [B, _1B, UB] else 0)\n", "data[\"区间委买1\"] = data[\"类型\"].apply(lambda x: 1 if x == _1B else 0)\n", "data[\"区间委买优\"] = data[\"类型\"].apply(lambda x: 1 if x == UB else 0)\n", "data[\"区间委卖\"] = data[\"类型\"].apply(lambda x: 1 if x in [S, _1S, US] else 0)\n", "data[\"区间委卖1\"] = data[\"类型\"].apply(lambda x: 1 if x == _1S else 0)\n", "data[\"区间委卖优\"] = data[\"类型\"].apply(lambda x: 1 if x == US else 0)\n", "data[\"区间委撤\"] = data[\"类型\"].apply(lambda x: 1 if x in [BC, SC] else 0)\n", "data[\"区间委撤买\"] = data[\"类型\"].apply(lambda x: 1 if x == BC else 0)\n", "data[\"区间委撤卖\"] = data[\"类型\"].apply(lambda x: 1 if x == SC else 0)\n", "\n", "# 手数\n", "data[\"区间手数\"] = data[\"手\"]\n", "data[\"区间手数波动率\"] = data[\"手\"]\n", "\n", "data[\"区间买手\"] = data[\"手\"] * data[\"区间委买\"]\n", "data[\"区间买手波动率\"] = data[\"区间买手\"]\n", "data[\"区间买手1\"] = data[\"手\"] * data[\"区间委买1\"]\n", "data[\"区间买手优\"] = data[\"手\"] * data[\"区间委买优\"]\n", "\n", "data[\"区间卖手\"] = data[\"手\"] * data[\"区间委卖\"]\n", "data[\"区间卖手波动率\"] = data[\"区间卖手\"]\n", "data[\"区间卖手1\"] = data[\"手\"] * data[\"区间委卖1\"]\n", "data[\"区间卖手优\"] = data[\"手\"] * data[\"区间委卖优\"]\n", "\n", "data[\"区间撤手\"] = data[\"手\"] * data[\"区间委撤\"]\n", "data[\"区间撤手买\"] = data[\"手\"] * data[\"区间委撤买\"]\n", "data[\"区间撤手卖\"] = data[\"手\"] * data[\"区间委撤卖\"]\n", "\n", "# 额\n", "data[\"区间撤额\"] = data[\"区间撤手\"] * data[\"价格\"]\n", "data[\"区间撤额买\"] = data[\"区间撤手买\"] * data[\"价格\"]\n", "data[\"区间撤额卖\"] = data[\"区间撤手卖\"] * data[\"价格\"]\n", "\n", "std_data = data.resample(\"3S\", closed=\"right\", label=\"right\").apply(\n", "    {\n", "        \"区间委托\": np.sum,\n", "        \"区间委买\": np.sum,\n", "        \"区间委买1\": np.sum,\n", "        \"区间委买优\": np.sum,\n", "        \"区间委卖\": np.sum,\n", "        \"区间委卖1\": np.sum,\n", "        \"区间委卖优\": np.sum,\n", "        \"区间委撤\": np.sum,\n", "        \"区间委撤买\": np.sum,\n", "        \"区间委撤卖\": np.sum,\n", "        \"区间手数\": np.sum,\n", "        \"区间手数波动率\": np.std,\n", "        \"区间买手\": np.sum,\n", "        \"区间买手波动率\": np.std,\n", "        \"区间买手1\": np.sum,\n", "        \"区间买手优\": np.sum,\n", "        \"区间卖手\": np.sum,\n", "        \"区间卖手波动率\": np.std,\n", "        \"区间卖手1\": np.sum,\n", "        \"区间卖手优\": np.sum,\n", "        \"区间撤手\": np.sum,\n", "        \"区间撤手买\": np.sum,\n", "        \"区间撤手卖\": np.sum,\n", "        \"区间撤额\": np.sum,\n", "        \"区间撤额买\": np.sum,\n", "        \"区间撤额卖\": np.sum,\n", "    }\n", ")\n", "\n", "std_data"]}, {"cell_type": "code", "execution_count": 35, "id": "aa835150", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>区间委卖1</th>\n", "      <th>区间委卖优</th>\n", "      <th>区间委撤</th>\n", "      <th>区间委撤买</th>\n", "      <th>区间委撤卖</th>\n", "      <th>...</th>\n", "      <th>区间均手卖</th>\n", "      <th>区间均手卖1</th>\n", "      <th>区间均手卖优</th>\n", "      <th>累计均手</th>\n", "      <th>累计均手买</th>\n", "      <th>累计均手买1</th>\n", "      <th>累计均手买优</th>\n", "      <th>累计均手卖</th>\n", "      <th>累计均手卖1</th>\n", "      <th>累计均手卖优</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>47547</td>\n", "      <td>10007</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27278</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10262</td>\n", "      <td>8971</td>\n", "      <td>1291</td>\n", "      <td>...</td>\n", "      <td>469.891561</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>455.352641</td>\n", "      <td>565.076646</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>469.891561</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>12843</td>\n", "      <td>3070</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8810</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>963</td>\n", "      <td>908</td>\n", "      <td>55</td>\n", "      <td>...</td>\n", "      <td>441.511918</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>461.467693</td>\n", "      <td>585.973694</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>462.963367</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>9619</td>\n", "      <td>1566</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6437</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1616</td>\n", "      <td>1032</td>\n", "      <td>584</td>\n", "      <td>...</td>\n", "      <td>516.697219</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>478.579768</td>\n", "      <td>599.695144</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>471.097049</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>5022</td>\n", "      <td>1023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3241</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>758</td>\n", "      <td>596</td>\n", "      <td>162</td>\n", "      <td>...</td>\n", "      <td>241.542734</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>465.127147</td>\n", "      <td>595.388612</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>454.840755</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>8025</td>\n", "      <td>3207</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4605</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>213</td>\n", "      <td>89</td>\n", "      <td>124</td>\n", "      <td>...</td>\n", "      <td>298.363518</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>444.633416</td>\n", "      <td>522.507603</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>440.535348</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>119.000000</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>1243.789352</td>\n", "      <td>1338.848526</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>857.592508</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>95.714286</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>1243.754705</td>\n", "      <td>1338.848526</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>857.556876</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>-1.000000</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>1243.742098</td>\n", "      <td>1338.826640</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>857.556876</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>50</td>\n", "      <td>40</td>\n", "      <td>10</td>\n", "      <td>...</td>\n", "      <td>-1.000000</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>1243.605791</td>\n", "      <td>1338.826640</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>857.556876</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>-1.000000</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>1243.596928</td>\n", "      <td>1338.826640</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>857.556876</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 70 columns</p>\n", "</div>"], "text/plain": ["                      区间委托   区间委买  区间委买1  区间委买优   区间委卖  区间委卖1  区间委卖优   区间委撤  \\\n", "时间                                                                            \n", "2023-09-01 09:30:03  47547  10007      0      0  27278      0      0  10262   \n", "2023-09-01 09:30:06  12843   3070      0      0   8810      0      0    963   \n", "2023-09-01 09:30:09   9619   1566      0      0   6437      0      0   1616   \n", "2023-09-01 09:30:12   5022   1023      0      0   3241      0      0    758   \n", "2023-09-01 09:30:15   8025   3207      0      0   4605      0      0    213   \n", "...                    ...    ...    ...    ...    ...    ...    ...    ...   \n", "2023-09-01 14:59:48      5      0      0      0      1      0      0      4   \n", "2023-09-01 14:59:51     11      0      0      0      7      0      0      4   \n", "2023-09-01 14:59:54      6      2      0      0      0      0      0      4   \n", "2023-09-01 14:59:57     50      0      0      0      0      0      0     50   \n", "2023-09-01 15:00:00      4      0      0      0      0      0      0      4   \n", "\n", "                     区间委撤买  区间委撤卖  ...       区间均手卖  区间均手卖1  区间均手卖优  \\\n", "时间                                 ...                               \n", "2023-09-01 09:30:03   8971   1291  ...  469.891561    -1.0    -1.0   \n", "2023-09-01 09:30:06    908     55  ...  441.511918    -1.0    -1.0   \n", "2023-09-01 09:30:09   1032    584  ...  516.697219    -1.0    -1.0   \n", "2023-09-01 09:30:12    596    162  ...  241.542734    -1.0    -1.0   \n", "2023-09-01 09:30:15     89    124  ...  298.363518    -1.0    -1.0   \n", "...                    ...    ...  ...         ...     ...     ...   \n", "2023-09-01 14:59:48      2      2  ...  119.000000    -1.0    -1.0   \n", "2023-09-01 14:59:51      3      1  ...   95.714286    -1.0    -1.0   \n", "2023-09-01 14:59:54      4      0  ...   -1.000000    -1.0    -1.0   \n", "2023-09-01 14:59:57     40     10  ...   -1.000000    -1.0    -1.0   \n", "2023-09-01 15:00:00      2      2  ...   -1.000000    -1.0    -1.0   \n", "\n", "                            累计均手        累计均手买  累计均手买1  累计均手买优       累计均手卖  \\\n", "时间                                                                          \n", "2023-09-01 09:30:03   455.352641   565.076646    -1.0    -1.0  469.891561   \n", "2023-09-01 09:30:06   461.467693   585.973694    -1.0    -1.0  462.963367   \n", "2023-09-01 09:30:09   478.579768   599.695144    -1.0    -1.0  471.097049   \n", "2023-09-01 09:30:12   465.127147   595.388612    -1.0    -1.0  454.840755   \n", "2023-09-01 09:30:15   444.633416   522.507603    -1.0    -1.0  440.535348   \n", "...                          ...          ...     ...     ...         ...   \n", "2023-09-01 14:59:48  1243.789352  1338.848526    -1.0    -1.0  857.592508   \n", "2023-09-01 14:59:51  1243.754705  1338.848526    -1.0    -1.0  857.556876   \n", "2023-09-01 14:59:54  1243.742098  1338.826640    -1.0    -1.0  857.556876   \n", "2023-09-01 14:59:57  1243.605791  1338.826640    -1.0    -1.0  857.556876   \n", "2023-09-01 15:00:00  1243.596928  1338.826640    -1.0    -1.0  857.556876   \n", "\n", "                     累计均手卖1  累计均手卖优  \n", "时间                                   \n", "2023-09-01 09:30:03    -1.0    -1.0  \n", "2023-09-01 09:30:06    -1.0    -1.0  \n", "2023-09-01 09:30:09    -1.0    -1.0  \n", "2023-09-01 09:30:12    -1.0    -1.0  \n", "2023-09-01 09:30:15    -1.0    -1.0  \n", "...                     ...     ...  \n", "2023-09-01 14:59:48    -1.0    -1.0  \n", "2023-09-01 14:59:51    -1.0    -1.0  \n", "2023-09-01 14:59:54    -1.0    -1.0  \n", "2023-09-01 14:59:57    -1.0    -1.0  \n", "2023-09-01 15:00:00    -1.0    -1.0  \n", "\n", "[4800 rows x 70 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# 删除最后一行 第一行\n", "std_data = std_data[1:-1]\n", "\n", "# 删除 11:30:00 - 13:00:00 之间的数据（不含）\n", "am = std_data.between_time(\"09:30:01\", \"11:30:00\")\n", "pm = std_data.between_time(\"13:00:01\", \"15:00:00\")\n", "std_data = std_data.loc[am.index.union(pm.index), :]\n", "\n", "# 增加累计数据\n", "std_data[\"累计委托\"] = std_data[\"区间委托\"].cumsum()\n", "std_data[\"累计委买\"] = std_data[\"区间委买\"].cumsum()\n", "std_data[\"累计委买1\"] = std_data[\"区间委买1\"].cumsum()\n", "std_data[\"累计委买优\"] = std_data[\"区间委买优\"].cumsum()\n", "std_data[\"累计委卖\"] = std_data[\"区间委卖\"].cumsum()\n", "std_data[\"累计委卖1\"] = std_data[\"区间委卖1\"].cumsum()\n", "std_data[\"累计委卖优\"] = std_data[\"区间委卖优\"].cumsum()\n", "std_data[\"累计委撤\"] = std_data[\"区间委撤\"].cumsum()\n", "std_data[\"累计委撤买\"] = std_data[\"区间委撤买\"].cumsum()\n", "std_data[\"累计委撤卖\"] = std_data[\"区间委撤卖\"].cumsum()\n", "\n", "std_data[\"累计手数\"] = std_data[\"区间手数\"].cumsum()\n", "std_data[\"累计买手\"] = std_data[\"区间买手\"].cumsum()\n", "std_data[\"累计买手1\"] = std_data[\"区间买手1\"].cumsum()\n", "std_data[\"累计买手优\"] = std_data[\"区间买手优\"].cumsum()\n", "std_data[\"累计卖手\"] = std_data[\"区间卖手\"].cumsum()\n", "std_data[\"累计卖手1\"] = std_data[\"区间卖手1\"].cumsum()\n", "std_data[\"累计卖手优\"] = std_data[\"区间卖手优\"].cumsum()\n", "std_data[\"累计撤手\"] = std_data[\"区间撤手\"].cumsum()\n", "std_data[\"累计撤手买\"] = std_data[\"区间撤手买\"].cumsum()\n", "std_data[\"累计撤手卖\"] = std_data[\"区间撤手卖\"].cumsum()\n", "\n", "std_data[\"区间撤VWAP\"] = std_data[\"区间撤额\"] / std_data[\"区间撤手\"]\n", "std_data[\"区间撤VWAP买\"] = std_data[\"区间撤额买\"] / std_data[\"区间撤手买\"]\n", "std_data[\"区间撤VWAP卖\"] = std_data[\"区间撤额卖\"] / std_data[\"区间撤手卖\"]\n", "std_data[\"累计撤VWAP\"] = (std_data[\"区间撤额\"].cumsum()) / (std_data[\"累计撤手\"])\n", "std_data[\"累计撤VWAP买\"] = (std_data[\"区间撤额买\"].cumsum()) / (std_data[\"累计撤手买\"])\n", "std_data[\"累计撤VWAP卖\"] = (std_data[\"区间撤额卖\"].cumsum()) / (std_data[\"累计撤手卖\"])\n", "\n", "std_data[\"区间撤比\"] = std_data[\"区间委撤\"] / std_data[\"区间委托\"]\n", "std_data[\"累计撤比\"] = std_data[\"累计委撤\"] / std_data[\"累计委托\"]\n", "std_data[\"区间撤比手\"] = std_data[\"区间撤手\"] / std_data[\"区间手数\"]\n", "std_data[\"累计撤比手\"] = std_data[\"累计撤手\"] / std_data[\"累计手数\"]\n", "\n", "std_data[\"区间均手\"] = std_data[\"区间手数\"] / std_data[\"区间委托\"]\n", "std_data[\"区间均手买\"] = std_data[\"区间买手\"] / std_data[\"区间委买\"]\n", "std_data[\"区间均手买1\"] = std_data[\"区间买手1\"] / std_data[\"区间委买1\"]\n", "std_data[\"区间均手买优\"] = std_data[\"区间买手优\"] / std_data[\"区间委买优\"]\n", "std_data[\"区间均手卖\"] = std_data[\"区间卖手\"] / std_data[\"区间委卖\"]\n", "std_data[\"区间均手卖1\"] = std_data[\"区间卖手1\"] / std_data[\"区间委卖1\"]\n", "std_data[\"区间均手卖优\"] = std_data[\"区间卖手优\"] / std_data[\"区间委卖优\"]\n", "\n", "std_data[\"累计均手\"] = std_data[\"累计手数\"] / std_data[\"累计委托\"]\n", "\n", "std_data[\"累计均手买\"] = std_data[\"累计买手\"] / std_data[\"累计委买\"]\n", "std_data[\"累计均手买1\"] = std_data[\"累计买手1\"] / std_data[\"累计委买1\"]\n", "std_data[\"累计均手买优\"] = std_data[\"累计买手优\"] / std_data[\"累计委买优\"]\n", "std_data[\"累计均手卖\"] = std_data[\"累计卖手\"] / std_data[\"累计委卖\"]\n", "std_data[\"累计均手卖1\"] = std_data[\"累计卖手1\"] / std_data[\"累计委卖1\"]\n", "std_data[\"累计均手卖优\"] = std_data[\"累计卖手优\"] / std_data[\"累计委卖优\"]\n", "\n", "# 填充 nan\n", "std_data = std_data.fillna(-1)\n", "std_data"]}, {"cell_type": "code", "execution_count": 36, "id": "9878330c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>...</th>\n", "      <th>区间卖手</th>\n", "      <th>区间卖手波动率</th>\n", "      <th>区间卖手1</th>\n", "      <th>区间卖手优</th>\n", "      <th>区间撤手</th>\n", "      <th>区间撤手买</th>\n", "      <th>区间撤手卖</th>\n", "      <th>区间撤额</th>\n", "      <th>区间撤额买</th>\n", "      <th>区间撤额卖</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>1</td>\n", "      <td>840.0</td>\n", "      <td>1.054</td>\n", "      <td>5.0</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>5.270</td>\n", "      <td>5.270</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>1.091</td>\n", "      <td>30.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>3</td>\n", "      <td>840.0</td>\n", "      <td>1.052</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>2.104</td>\n", "      <td>2.104</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>4</td>\n", "      <td>840.0</td>\n", "      <td>1.041</td>\n", "      <td>21.0</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>21.0</td>\n", "      <td>0.0</td>\n", "      <td>21.861</td>\n", "      <td>21.861</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>5</td>\n", "      <td>840.0</td>\n", "      <td>1.052</td>\n", "      <td>10.0</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>10.0</td>\n", "      <td>0.0</td>\n", "      <td>10.520</td>\n", "      <td>10.520</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 31 columns</p>\n", "</div>"], "text/plain": ["                    序号   在委时间     价格     手   类型  区间委托  区间委买  区间委买1  区间委买优  \\\n", "时间                                                                          \n", "2023-09-01 09:30:01  1  840.0  1.054   5.0  2.0     1     0      0      0   \n", "2023-09-01 09:30:01  2    0.0  1.091  30.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:01  3  840.0  1.052   2.0  2.0     1     0      0      0   \n", "2023-09-01 09:30:01  4  840.0  1.041  21.0  2.0     1     0      0      0   \n", "2023-09-01 09:30:01  5  840.0  1.052  10.0  2.0     1     0      0      0   \n", "\n", "                     区间委卖  ...  区间卖手  区间卖手波动率  区间卖手1  区间卖手优  区间撤手  区间撤手买  \\\n", "时间                         ...                                             \n", "2023-09-01 09:30:01     0  ...   0.0      0.0    0.0    0.0   5.0    5.0   \n", "2023-09-01 09:30:01     1  ...  30.0     30.0    0.0    0.0   0.0    0.0   \n", "2023-09-01 09:30:01     0  ...   0.0      0.0    0.0    0.0   2.0    2.0   \n", "2023-09-01 09:30:01     0  ...   0.0      0.0    0.0    0.0  21.0   21.0   \n", "2023-09-01 09:30:01     0  ...   0.0      0.0    0.0    0.0  10.0   10.0   \n", "\n", "                     区间撤手卖    区间撤额   区间撤额买  区间撤额卖  \n", "时间                                                 \n", "2023-09-01 09:30:01    0.0   5.270   5.270    0.0  \n", "2023-09-01 09:30:01    0.0   0.000   0.000    0.0  \n", "2023-09-01 09:30:01    0.0   2.104   2.104    0.0  \n", "2023-09-01 09:30:01    0.0  21.861  21.861    0.0  \n", "2023-09-01 09:30:01    0.0  10.520  10.520    0.0  \n", "\n", "[5 rows x 31 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 61, "id": "94f6825f", "metadata": {}, "outputs": [], "source": ["def top_to_cols(_trade, _type_name, sort_by, dt, n):\n", "    \"\"\"\n", "    将筛选出来的订单/成交 转成 一行\n", "    _trade: 订单/成交\n", "    _type_name: 买/卖 用于列命名\n", "    sort_by: 排序列名 用于列命名\n", "    dt: 所属的时间索引\n", "    n: top n\n", "    \"\"\"\n", "    # 转置到一行中\n", "    p = _trade[['价格']].T.reset_index(drop=True)\n", "    p.columns = [f'委{_type_name + sort_by[0]}{i+1}价' for i in range(n)]\n", "    v = _trade[['手']].T.reset_index(drop=True)\n", "    v.columns = [f'委{_type_name + sort_by[0]}{i+1}手' for i in range(n)]\n", "\n", "    # 合并\n", "    _top_deal = pd.concat([p, v], axis=1)\n", "\n", "    # 排序\n", "    columns = list(_top_deal)\n", "    fix_columns = []\n", "    for i in range(n):\n", "        fix_columns.append(columns[i])\n", "        fix_columns.append(columns[i+n])\n", "\n", "    _top_deal = _top_deal[fix_columns]\n", "    \n", "    # 统一时间索引\n", "    _top_deal['时间'] = dt\n", "    _top_deal.set_index('时间', inplace=True)\n", "\n", "    return _top_deal"]}, {"cell_type": "code", "execution_count": 62, "id": "c2ac5859", "metadata": {}, "outputs": [], "source": ["def top_order(end_dt, data, n=100):\n", "    \"\"\"\n", "    买卖单中最大 数量top\n", "    买卖单中最 高/低价top\n", "    \"\"\"\n", "    # 时间筛选\n", "    begin_dt = end_dt-<PERSON><PERSON><PERSON>(seconds=3)\n", "    time_data = data[(data.index>begin_dt) & (data.index<=end_dt)].reset_index(drop=True)\n", "    \n", "    # 添加到足够的数量\n", "    if len(time_data)<n:\n", "        time_data = pd.concat([pd.DataFrame(range(n)), time_data], axis=1).iloc[:,1:]\n", "        time_data.fillna(-1, inplace=True)\n", "    \n", "    # 291 ms\n", "    # 买卖筛选\n", "    _buy_type_df = pd.Series([B, _1B, UB], name=\"类型\").to_frame()\n", "    _sell_type_df = pd.Series([S, _1S, US], name=\"类型\").to_frame()\n", "    buy_trade = time_data.merge(_buy_type_df, on=\"类型\")\n", "    sell_trade = time_data.merge(_sell_type_df, on=\"类型\")\n", "    \n", "    # 排序\n", "    _buy_type_name = \"买\"\n", "    _sell_type_name = \"卖\"\n", "    sort_by_hand = \"手\"\n", "    sort_by_price = \"价格\"\n", "    ascending_hand = False\n", "    ascending_buy = False\n", "    ascending_sell = True\n", "    buy_trade_vol = buy_trade.sort_values(sort_by_hand, ascending=ascending_hand).iloc[:n,2:4].reset_index(drop=True)\n", "    sell_trade_vol = sell_trade.sort_values(sort_by_hand, ascending=ascending_hand).iloc[:n,2:4].reset_index(drop=True)\n", "    buy_trade_price = buy_trade.sort_values(sort_by_price, ascending=ascending_buy).iloc[:n,2:4].reset_index(drop=True)\n", "    sell_trade_price = sell_trade.sort_values(sort_by_price, ascending=ascending_sell).iloc[:n,2:4].reset_index(drop=True)\n", "    \n", "    \n", "    # return pd.DataFrame()\n", "    \n", "    # 合并返回\n", "    return pd.concat([\n", "        top_to_cols(buy_trade_vol,_buy_type_name,sort_by_hand,end_dt, n),\n", "        top_to_cols(buy_trade_price,_buy_type_name,sort_by_price,end_dt, n),\n", "        top_to_cols(sell_trade_vol,_sell_type_name,sort_by_hand,end_dt, n),\n", "        top_to_cols(sell_trade_price,_sell_type_name,sort_by_price,end_dt, n),\n", "        ], axis=1)"]}, {"cell_type": "code", "execution_count": 63, "id": "ed6ad868", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["223 ms ± 16.9 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "# total: 11min 23s\n", "# 区间内成交量大到小排序前100笔买卖交易 价格/数量\n", "top_data = pd.DataFrame()\n", "for idx, end_dt in enumerate(std_data.index[:5]):\n", "#     if idx%500 == 0:\n", "#         print(f'{end_dt}')\n", "\n", "    top_data = pd.concat([top_data, top_order(end_dt, data, 100)], axis=1)\n", "\n", "top_data"]}, {"cell_type": "code", "execution_count": 51, "id": "10eb0bce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 31.2 ms\n", "Wall time: 27.2 ms\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>...</th>\n", "      <th>区间卖手</th>\n", "      <th>区间卖手波动率</th>\n", "      <th>区间卖手1</th>\n", "      <th>区间卖手优</th>\n", "      <th>区间撤手</th>\n", "      <th>区间撤手买</th>\n", "      <th>区间撤手卖</th>\n", "      <th>区间撤额</th>\n", "      <th>区间撤额买</th>\n", "      <th>区间撤额卖</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>1.090</td>\n", "      <td>25.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>25.0</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>1.163</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>9.0</td>\n", "      <td>9.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>8</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>359.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>359.0</td>\n", "      <td>359.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>9</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>10</td>\n", "      <td>0.0</td>\n", "      <td>1.082</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>11</td>\n", "      <td>16.0</td>\n", "      <td>1.066</td>\n", "      <td>130.0</td>\n", "      <td>3.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>138.58</td>\n", "      <td>0.0</td>\n", "      <td>138.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>93.0</td>\n", "      <td>93.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3569 rows × 31 columns</p>\n", "</div>"], "text/plain": ["                     序号  在委时间     价格      手   类型  区间委托  区间委买  区间委买1  区间委买优  \\\n", "时间                                                                           \n", "2023-09-01 09:30:19   1   0.0  1.064    5.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   2   0.0  1.090   25.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   3   0.0  1.163  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   4   0.0  1.061  150.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:19   5   0.0  1.064    9.0  1.0     1     0      0      0   \n", "...                  ..   ...    ...    ...  ...   ...   ...    ...    ...   \n", "2023-09-01 09:30:21   8   0.0  1.064  359.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21   9   0.0  1.061    1.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:21  10   0.0  1.082  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21  11  16.0  1.066  130.0  3.0     1     0      0      0   \n", "2023-09-01 09:30:21  12   0.0  1.065   93.0  1.0     1     0      0      0   \n", "\n", "                     区间委卖  ...   区间卖手  区间卖手波动率  区间卖手1  区间卖手优   区间撤手  区间撤手买  \\\n", "时间                         ...                                               \n", "2023-09-01 09:30:19     1  ...    5.0      5.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...   25.0     25.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...    9.0      9.0    0.0    0.0    0.0    0.0   \n", "...                   ...  ...    ...      ...    ...    ...    ...    ...   \n", "2023-09-01 09:30:21     1  ...  359.0    359.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0  130.0    0.0   \n", "2023-09-01 09:30:21     1  ...   93.0     93.0    0.0    0.0    0.0    0.0   \n", "\n", "                     区间撤手卖    区间撤额  区间撤额买   区间撤额卖  \n", "时间                                                 \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "...                    ...     ...    ...     ...  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21  130.0  138.58    0.0  138.58  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "\n", "[3569 rows x 31 columns]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "_trade = data.query(f'index>\"{end_dt-<PERSON><PERSON><PERSON>(seconds=3)}\" and index<=\"{end_dt}\"')\n", "_trade"]}, {"cell_type": "code", "execution_count": 54, "id": "c37e7d76", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>时间</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <th>...</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <th>2023-09-01 09:30:21</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>价格</th>\n", "      <td>1.064</td>\n", "      <td>1.09</td>\n", "      <td>1.163</td>\n", "      <td>1.061</td>\n", "      <td>1.064</td>\n", "      <td>1.074</td>\n", "      <td>0.985</td>\n", "      <td>1.064</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>1.064</td>\n", "      <td>1.072</td>\n", "      <td>1.071</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>1.061</td>\n", "      <td>1.082</td>\n", "      <td>1.066</td>\n", "      <td>1.065</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 3569 columns</p>\n", "</div>"], "text/plain": ["时间  2023-09-01 09:30:19  2023-09-01 09:30:19  2023-09-01 09:30:19  \\\n", "价格                1.064                 1.09                1.163   \n", "\n", "时间  2023-09-01 09:30:19  2023-09-01 09:30:19  2023-09-01 09:30:19  \\\n", "价格                1.061                1.064                1.074   \n", "\n", "时间  2023-09-01 09:30:19  2023-09-01 09:30:19  2023-09-01 09:30:19  \\\n", "价格                0.985                1.064                1.057   \n", "\n", "时间  2023-09-01 09:30:19  ...  2023-09-01 09:30:21  2023-09-01 09:30:21  \\\n", "价格                1.064  ...                1.061                1.064   \n", "\n", "时间  2023-09-01 09:30:21  2023-09-01 09:30:21  2023-09-01 09:30:21  \\\n", "价格                1.072                1.071                1.066   \n", "\n", "时间  2023-09-01 09:30:21  2023-09-01 09:30:21  2023-09-01 09:30:21  \\\n", "价格                1.064                1.061                1.082   \n", "\n", "时间  2023-09-01 09:30:21  2023-09-01 09:30:21  \n", "价格                1.066                1.065  \n", "\n", "[1 rows x 3569 columns]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["p = _trade[['价格']].T\n", "p"]}, {"cell_type": "code", "execution_count": 48, "id": "ea95a81a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 5, 7, 0, 4, 6)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["S, _1S, US, B, _1B, UB"]}, {"cell_type": "code", "execution_count": 49, "id": "1018b712", "metadata": {}, "outputs": [{"data": {"text/plain": ["(Timestamp('2023-09-01 09:30:19'),\n", " Timestamp('2023-09-01 09:30:21'),\n", " [1, 5, 7],\n", " '手',\n", " True)"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["_type_list = [S, _1S, US]\n", "sort_by = \"手\"\n", "ascending = True\n", "dt = data.index.to_series()\n", "end_dt = std_data.index[6]\n", "begin_dt = end_dt- <PERSON><PERSON><PERSON>(seconds=2)\n", "begin_dt, end_dt, _type_list, sort_by, ascending"]}, {"cell_type": "code", "execution_count": 216, "id": "81690f01", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>...</th>\n", "      <th>区间卖手</th>\n", "      <th>区间卖手波动率</th>\n", "      <th>区间卖手1</th>\n", "      <th>区间卖手优</th>\n", "      <th>区间撤手</th>\n", "      <th>区间撤手买</th>\n", "      <th>区间撤手卖</th>\n", "      <th>区间撤额</th>\n", "      <th>区间撤额买</th>\n", "      <th>区间撤额卖</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>1.090</td>\n", "      <td>25.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>25.0</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>1.163</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>9.0</td>\n", "      <td>9.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>8</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>359.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>359.0</td>\n", "      <td>359.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>9</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>10</td>\n", "      <td>0.0</td>\n", "      <td>1.082</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>11</td>\n", "      <td>16.0</td>\n", "      <td>1.066</td>\n", "      <td>130.0</td>\n", "      <td>3.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>138.58</td>\n", "      <td>0.0</td>\n", "      <td>138.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>93.0</td>\n", "      <td>93.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3569 rows × 31 columns</p>\n", "</div>"], "text/plain": ["                     序号  在委时间     价格      手   类型  区间委托  区间委买  区间委买1  区间委买优  \\\n", "时间                                                                           \n", "2023-09-01 09:30:19   1   0.0  1.064    5.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   2   0.0  1.090   25.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   3   0.0  1.163  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   4   0.0  1.061  150.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:19   5   0.0  1.064    9.0  1.0     1     0      0      0   \n", "...                  ..   ...    ...    ...  ...   ...   ...    ...    ...   \n", "2023-09-01 09:30:21   8   0.0  1.064  359.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21   9   0.0  1.061    1.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:21  10   0.0  1.082  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21  11  16.0  1.066  130.0  3.0     1     0      0      0   \n", "2023-09-01 09:30:21  12   0.0  1.065   93.0  1.0     1     0      0      0   \n", "\n", "                     区间委卖  ...   区间卖手  区间卖手波动率  区间卖手1  区间卖手优   区间撤手  区间撤手买  \\\n", "时间                         ...                                               \n", "2023-09-01 09:30:19     1  ...    5.0      5.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...   25.0     25.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...    9.0      9.0    0.0    0.0    0.0    0.0   \n", "...                   ...  ...    ...      ...    ...    ...    ...    ...   \n", "2023-09-01 09:30:21     1  ...  359.0    359.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0  130.0    0.0   \n", "2023-09-01 09:30:21     1  ...   93.0     93.0    0.0    0.0    0.0    0.0   \n", "\n", "                     区间撤手卖    区间撤额  区间撤额买   区间撤额卖  \n", "时间                                                 \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "...                    ...     ...    ...     ...  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21  130.0  138.58    0.0  138.58  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "\n", "[3569 rows x 31 columns]"]}, "execution_count": 216, "metadata": {}, "output_type": "execute_result"}], "source": ["data[end_dt- timedelta(seconds=2):end_dt]"]}, {"cell_type": "code", "execution_count": 283, "id": "fad25e0c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.067</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.067</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.067</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.067</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.067</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>1.072</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 2 columns</p>\n", "</div>"], "text/plain": ["       价格     手\n", "0   1.067   1.0\n", "1   1.067   1.0\n", "2   1.067   1.0\n", "3   1.067   1.0\n", "4   1.067   1.0\n", "..    ...   ...\n", "95  1.072  50.0\n", "96  1.065  93.0\n", "97  1.065  93.0\n", "98  1.065  93.0\n", "99  1.065  93.0\n", "\n", "[100 rows x 2 columns]"]}, "execution_count": 283, "metadata": {}, "output_type": "execute_result"}], "source": ["# %%timeit\n", "# 54.2 ms\n", "data[(dt>begin_dt) & (dt<=end_dt) & data['类型'].isin(_type_list)].sort_values(sort_by, ascending=ascending).iloc[:100,2:4].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 281, "id": "555bb364", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [价格, 手]\n", "Index: []"]}, "execution_count": 281, "metadata": {}, "output_type": "execute_result"}], "source": ["# %%timeit\n", "# 9.31 ms\n", "_type_list2 = pd.Series([S, _1S, US])\n", "data[(dt>begin_dt) & (dt<=end_dt) & data['类型'].isin(_type_list2)].sort_values(sort_by, ascending=ascending).iloc[:100,2:4].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 194, "id": "75a40495", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 2 columns</p>\n", "</div>"], "text/plain": ["    在委时间     价格\n", "0    0.0  1.064\n", "1    0.0  1.064\n", "2    0.0  1.064\n", "3    0.0  1.064\n", "4    0.0  1.064\n", "..   ...    ...\n", "95   0.0  1.064\n", "96   0.0  1.064\n", "97   0.0  1.064\n", "98   0.0  1.064\n", "99   0.0  1.064\n", "\n", "[100 rows x 2 columns]"]}, "execution_count": 194, "metadata": {}, "output_type": "execute_result"}], "source": ["# %%timeit\n", "# 6.78 ms\n", "_type_list3 = pd.Series([S, _1S, US], name=\"类型\").to_frame()\n", "data[(dt>begin_dt) & (dt<=end_dt)].merge(_type_list3, on=\"类型\").sort_values(sort_by, ascending=ascending).iloc[:100,1:3].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 184, "id": "bc977f40", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   0\n", "0  1\n", "1  5\n", "2  7"]}, "execution_count": 184, "metadata": {}, "output_type": "execute_result"}], "source": ["_type_list3"]}, {"cell_type": "code", "execution_count": 168, "id": "7277493a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "      <th>区间委托</th>\n", "      <th>区间委买</th>\n", "      <th>区间委买1</th>\n", "      <th>区间委买优</th>\n", "      <th>区间委卖</th>\n", "      <th>...</th>\n", "      <th>区间卖手</th>\n", "      <th>区间卖手波动率</th>\n", "      <th>区间卖手1</th>\n", "      <th>区间卖手优</th>\n", "      <th>区间撤手</th>\n", "      <th>区间撤手买</th>\n", "      <th>区间撤手卖</th>\n", "      <th>区间撤额</th>\n", "      <th>区间撤额买</th>\n", "      <th>区间撤额卖</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>1.090</td>\n", "      <td>25.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>25.0</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>1.163</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:19</th>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>9.0</td>\n", "      <td>9.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>8</td>\n", "      <td>0.0</td>\n", "      <td>1.064</td>\n", "      <td>359.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>359.0</td>\n", "      <td>359.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>9</td>\n", "      <td>0.0</td>\n", "      <td>1.061</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>10</td>\n", "      <td>0.0</td>\n", "      <td>1.082</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>11</td>\n", "      <td>16.0</td>\n", "      <td>1.066</td>\n", "      <td>130.0</td>\n", "      <td>3.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>0.0</td>\n", "      <td>130.0</td>\n", "      <td>138.58</td>\n", "      <td>0.0</td>\n", "      <td>138.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:21</th>\n", "      <td>12</td>\n", "      <td>0.0</td>\n", "      <td>1.065</td>\n", "      <td>93.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>93.0</td>\n", "      <td>93.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3569 rows × 31 columns</p>\n", "</div>"], "text/plain": ["                     序号  在委时间     价格      手   类型  区间委托  区间委买  区间委买1  区间委买优  \\\n", "时间                                                                           \n", "2023-09-01 09:30:19   1   0.0  1.064    5.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   2   0.0  1.090   25.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   3   0.0  1.163  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:19   4   0.0  1.061  150.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:19   5   0.0  1.064    9.0  1.0     1     0      0      0   \n", "...                  ..   ...    ...    ...  ...   ...   ...    ...    ...   \n", "2023-09-01 09:30:21   8   0.0  1.064  359.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21   9   0.0  1.061    1.0  0.0     1     1      0      0   \n", "2023-09-01 09:30:21  10   0.0  1.082  100.0  1.0     1     0      0      0   \n", "2023-09-01 09:30:21  11  16.0  1.066  130.0  3.0     1     0      0      0   \n", "2023-09-01 09:30:21  12   0.0  1.065   93.0  1.0     1     0      0      0   \n", "\n", "                     区间委卖  ...   区间卖手  区间卖手波动率  区间卖手1  区间卖手优   区间撤手  区间撤手买  \\\n", "时间                         ...                                               \n", "2023-09-01 09:30:19     1  ...    5.0      5.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...   25.0     25.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:19     1  ...    9.0      9.0    0.0    0.0    0.0    0.0   \n", "...                   ...  ...    ...      ...    ...    ...    ...    ...   \n", "2023-09-01 09:30:21     1  ...  359.0    359.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     1  ...  100.0    100.0    0.0    0.0    0.0    0.0   \n", "2023-09-01 09:30:21     0  ...    0.0      0.0    0.0    0.0  130.0    0.0   \n", "2023-09-01 09:30:21     1  ...   93.0     93.0    0.0    0.0    0.0    0.0   \n", "\n", "                     区间撤手卖    区间撤额  区间撤额买   区间撤额卖  \n", "时间                                                 \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:19    0.0    0.00    0.0    0.00  \n", "...                    ...     ...    ...     ...  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "2023-09-01 09:30:21  130.0  138.58    0.0  138.58  \n", "2023-09-01 09:30:21    0.0    0.00    0.0    0.00  \n", "\n", "[3569 rows x 31 columns]"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["data[(dt>begin_dt) & (dt<=end_dt)]"]}, {"cell_type": "code", "execution_count": 165, "id": "8b3ae24a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Timestamp('2023-09-01 09:30:21')"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["std_data.index[6]"]}, {"cell_type": "code", "execution_count": 132, "id": "3ebcb8db", "metadata": {}, "outputs": [{"data": {"text/plain": ["12.479999999999999"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["4800*156/1000/60"]}, {"cell_type": "code", "execution_count": 106, "id": "2c46c403", "metadata": {}, "outputs": [{"data": {"text/plain": ["'dt>2023-09-01 09:34:57 and dt<=2023-09-01 09:35:00'"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["f'dt>{str(begin_dt)} and dt<={str(end_dt)}'"]}, {"cell_type": "code", "execution_count": 89, "id": "139a8e10", "metadata": {}, "outputs": [{"data": {"text/plain": ["5.373831775700935"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["11.5/2.14"]}, {"cell_type": "code", "execution_count": 15, "id": "0e916090", "metadata": {}, "outputs": [{"data": {"text/plain": ["序号          object\n", "在委时间       float64\n", "价格         float64\n", "手          float64\n", "类型         float64\n", "区间委托         int64\n", "区间委买         int64\n", "区间委买1        int64\n", "区间委买优        int64\n", "区间委卖         int64\n", "区间委卖1        int64\n", "区间委卖优        int64\n", "区间委撤         int64\n", "区间委撤买        int64\n", "区间委撤卖        int64\n", "区间手数       float64\n", "区间手数波动率    float64\n", "区间买手       float64\n", "区间买手波动率    float64\n", "区间买手1      float64\n", "区间买手优      float64\n", "区间卖手       float64\n", "区间卖手波动率    float64\n", "区间卖手1      float64\n", "区间卖手优      float64\n", "区间撤手       float64\n", "区间撤手买      float64\n", "区间撤手卖      float64\n", "区间撤额       float64\n", "区间撤额买      float64\n", "区间撤额卖      float64\n", "dtype: object"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": 16, "id": "cb99ee69", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ZBWT_区间委托</th>\n", "      <th>ZBWT_区间委买</th>\n", "      <th>ZBWT_区间委买1</th>\n", "      <th>ZBWT_区间委买优</th>\n", "      <th>ZBWT_区间委卖</th>\n", "      <th>ZBWT_区间委卖1</th>\n", "      <th>ZBWT_区间委卖优</th>\n", "      <th>ZBWT_区间委撤</th>\n", "      <th>ZBWT_区间委撤买</th>\n", "      <th>ZBWT_区间委撤卖</th>\n", "      <th>...</th>\n", "      <th>ZBWT_委卖价96价</th>\n", "      <th>ZBWT_委卖价96手</th>\n", "      <th>ZBWT_委卖价97价</th>\n", "      <th>ZBWT_委卖价97手</th>\n", "      <th>ZBWT_委卖价98价</th>\n", "      <th>ZBWT_委卖价98手</th>\n", "      <th>ZBWT_委卖价99价</th>\n", "      <th>ZBWT_委卖价99手</th>\n", "      <th>ZBWT_委卖价100价</th>\n", "      <th>ZBWT_委卖价100手</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>47547</td>\n", "      <td>10007</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27278</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10262</td>\n", "      <td>8971</td>\n", "      <td>1291</td>\n", "      <td>...</td>\n", "      <td>1.080</td>\n", "      <td>1.0</td>\n", "      <td>1.163</td>\n", "      <td>1.0</td>\n", "      <td>1.065</td>\n", "      <td>1.0</td>\n", "      <td>1.064</td>\n", "      <td>1.0</td>\n", "      <td>1.065</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>12843</td>\n", "      <td>3070</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8810</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>963</td>\n", "      <td>908</td>\n", "      <td>55</td>\n", "      <td>...</td>\n", "      <td>1.064</td>\n", "      <td>1.0</td>\n", "      <td>1.074</td>\n", "      <td>1.0</td>\n", "      <td>1.070</td>\n", "      <td>1.0</td>\n", "      <td>1.080</td>\n", "      <td>1.0</td>\n", "      <td>1.070</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>9619</td>\n", "      <td>1566</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6437</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1616</td>\n", "      <td>1032</td>\n", "      <td>584</td>\n", "      <td>...</td>\n", "      <td>1.098</td>\n", "      <td>3.0</td>\n", "      <td>1.066</td>\n", "      <td>3.0</td>\n", "      <td>1.098</td>\n", "      <td>3.0</td>\n", "      <td>1.066</td>\n", "      <td>3.0</td>\n", "      <td>1.098</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>5022</td>\n", "      <td>1023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3241</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>758</td>\n", "      <td>596</td>\n", "      <td>162</td>\n", "      <td>...</td>\n", "      <td>1.065</td>\n", "      <td>3.0</td>\n", "      <td>1.065</td>\n", "      <td>3.0</td>\n", "      <td>1.065</td>\n", "      <td>3.0</td>\n", "      <td>1.065</td>\n", "      <td>3.0</td>\n", "      <td>1.065</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>8025</td>\n", "      <td>3207</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4605</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>213</td>\n", "      <td>89</td>\n", "      <td>124</td>\n", "      <td>...</td>\n", "      <td>1.066</td>\n", "      <td>1.0</td>\n", "      <td>1.064</td>\n", "      <td>1.0</td>\n", "      <td>1.066</td>\n", "      <td>1.0</td>\n", "      <td>1.066</td>\n", "      <td>1.0</td>\n", "      <td>1.066</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>50</td>\n", "      <td>40</td>\n", "      <td>10</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 870 columns</p>\n", "</div>"], "text/plain": ["                     ZBWT_区间委托  ZBWT_区间委买  ZBWT_区间委买1  ZBWT_区间委买优  ZBWT_区间委卖  \\\n", "时间                                                                             \n", "2023-09-01 09:30:03      47547      10007           0           0      27278   \n", "2023-09-01 09:30:06      12843       3070           0           0       8810   \n", "2023-09-01 09:30:09       9619       1566           0           0       6437   \n", "2023-09-01 09:30:12       5022       1023           0           0       3241   \n", "2023-09-01 09:30:15       8025       3207           0           0       4605   \n", "...                        ...        ...         ...         ...        ...   \n", "2023-09-01 14:59:48          5          0           0           0          1   \n", "2023-09-01 14:59:51         11          0           0           0          7   \n", "2023-09-01 14:59:54          6          2           0           0          0   \n", "2023-09-01 14:59:57         50          0           0           0          0   \n", "2023-09-01 15:00:00          4          0           0           0          0   \n", "\n", "                     ZBWT_区间委卖1  ZBWT_区间委卖优  ZBWT_区间委撤  ZBWT_区间委撤买  \\\n", "时间                                                                   \n", "2023-09-01 09:30:03           0           0      10262        8971   \n", "2023-09-01 09:30:06           0           0        963         908   \n", "2023-09-01 09:30:09           0           0       1616        1032   \n", "2023-09-01 09:30:12           0           0        758         596   \n", "2023-09-01 09:30:15           0           0        213          89   \n", "...                         ...         ...        ...         ...   \n", "2023-09-01 14:59:48           0           0          4           2   \n", "2023-09-01 14:59:51           0           0          4           3   \n", "2023-09-01 14:59:54           0           0          4           4   \n", "2023-09-01 14:59:57           0           0         50          40   \n", "2023-09-01 15:00:00           0           0          4           2   \n", "\n", "                     ZBWT_区间委撤卖  ...  ZBWT_委卖价96价  ZBWT_委卖价96手  ZBWT_委卖价97价  \\\n", "时间                               ...                                          \n", "2023-09-01 09:30:03        1291  ...        1.080          1.0        1.163   \n", "2023-09-01 09:30:06          55  ...        1.064          1.0        1.074   \n", "2023-09-01 09:30:09         584  ...        1.098          3.0        1.066   \n", "2023-09-01 09:30:12         162  ...        1.065          3.0        1.065   \n", "2023-09-01 09:30:15         124  ...        1.066          1.0        1.064   \n", "...                         ...  ...          ...          ...          ...   \n", "2023-09-01 14:59:48           2  ...        0.000          0.0        0.000   \n", "2023-09-01 14:59:51           1  ...        0.000          0.0        0.000   \n", "2023-09-01 14:59:54           0  ...        0.000          0.0        0.000   \n", "2023-09-01 14:59:57          10  ...        0.000          0.0        0.000   \n", "2023-09-01 15:00:00           2  ...        0.000          0.0        0.000   \n", "\n", "                     ZBWT_委卖价97手  ZBWT_委卖价98价  ZBWT_委卖价98手  ZBWT_委卖价99价  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03          1.0        1.065          1.0        1.064   \n", "2023-09-01 09:30:06          1.0        1.070          1.0        1.080   \n", "2023-09-01 09:30:09          3.0        1.098          3.0        1.066   \n", "2023-09-01 09:30:12          3.0        1.065          3.0        1.065   \n", "2023-09-01 09:30:15          1.0        1.066          1.0        1.066   \n", "...                          ...          ...          ...          ...   \n", "2023-09-01 14:59:48          0.0        0.000          0.0        0.000   \n", "2023-09-01 14:59:51          0.0        0.000          0.0        0.000   \n", "2023-09-01 14:59:54          0.0        0.000          0.0        0.000   \n", "2023-09-01 14:59:57          0.0        0.000          0.0        0.000   \n", "2023-09-01 15:00:00          0.0        0.000          0.0        0.000   \n", "\n", "                     ZBWT_委卖价99手  ZBWT_委卖价100价  ZBWT_委卖价100手  \n", "时间                                                            \n", "2023-09-01 09:30:03          1.0         1.065           1.0  \n", "2023-09-01 09:30:06          1.0         1.070           1.0  \n", "2023-09-01 09:30:09          3.0         1.098           3.0  \n", "2023-09-01 09:30:12          3.0         1.065           3.0  \n", "2023-09-01 09:30:15          1.0         1.066           1.0  \n", "...                          ...           ...           ...  \n", "2023-09-01 14:59:48          0.0         0.000           0.0  \n", "2023-09-01 14:59:51          0.0         0.000           0.0  \n", "2023-09-01 14:59:54          0.0         0.000           0.0  \n", "2023-09-01 14:59:57          0.0         0.000           0.0  \n", "2023-09-01 15:00:00          0.0         0.000           0.0  \n", "\n", "[4800 rows x 870 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 合并数据\n", "std_data = pd.concat([std_data, top_data], axis=1)\n", "\n", "# 添加前缀\n", "std_data.columns = [f'ZBWT_{i}' for i in list(std_data)]\n", "std_data"]}, {"cell_type": "code", "execution_count": null, "id": "2b19f0a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "20a62602", "metadata": {}, "outputs": [], "source": ["std_data.to_csv(\"std_data_zbwt.csv\")"]}, {"cell_type": "code", "execution_count": 40, "id": "a36a977f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["apply_set() took 0.0009801387786865234 seconds apply_list() took 1.444631576538086 seconds and isin() took 0.5813283920288086 seconds.\n"]}], "source": ["import pandas as pd\n", "import time\n", "\n", "squares_set = set(a**2 for a in range(1000000))\n", "squares_list = [a**2 for a in range(1000000)]\n", "series = pd.Series(range(100))\n", "\n", "start = time.time()\n", "apply_result = series.apply(lambda x: x in squares_set)\n", "apply1_end = time.time()\n", "apply_result = series.apply(lambda x: x in squares_list)\n", "apply2_end = time.time()\n", "isin_result = series.isin(squares)\n", "isin_end = time.time()\n", "\n", "assert((apply_result==isin_result).all())\n", "\n", "print(\"apply_set() took {} seconds apply_list() took {} seconds and isin() took {} seconds.\".format(apply1_end - start,apply2_end-apply1_end,isin_end - apply2_end))\n"]}, {"cell_type": "code", "execution_count": null, "id": "89cb28ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}