"""
 弃用
"""

import pandas as pd
import numpy as np
import datetime
import os, pickle
import shutil

from feature.tool import get_time_point

def check_cols_num(file, error_path):
    # 检查数据列数 与列名是否匹配
    # 打开的请求文件
    raws = []
    with open(file, "r", encoding="gbk") as f:
        raws = f.readlines()

    # col 列数
    raw_cols = len(raws[0].split(","))

    # 遍历rows 如何列表的长度大于 row_cols 则删除该行, 保存文件到 error 文件夹中
    new_rows = []
    if_error_cols = False
    for i in raws:
        if len(i.split(",")) > raw_cols:
            if_error_cols = True
            continue
        new_rows.append(i)

    if if_error_cols:
        os.makedirs(error_path, exist_ok=True)
        # 拷贝文件到 error 文件夹中
        shutil.copy(
            file,
            os.path.join(
                error_path,
                os.path.basename(file)
            ),
        )

        # 不重写 抛出异常
        raise Exception("文件列数异常")
        # # 重写文件
        # with open(file, "w", encoding="gbk") as f:
        #     f.writelines(new_rows)


def check_nan_cols(file, data, error_path, fillnan=False, need_check_nan_cols=[]):
    # if fillnan:
    #     # 用前值填充缺失值
    #     data.fillna(method='ffill', inplace=True)
    # else:
    # 检查是否有缺失的值
    if data.isnull().any().any():
        os.makedirs(error_path, exist_ok=True)
        # 拷贝文件到 error 文件夹中
        error_bak = os.path.join(
            error_path,
            os.path.basename(file)
        )
        shutil.copy(
            file,
            error_bak,
        )

        if len(need_check_nan_cols):
            _check_raw_data = data.loc[:, need_check_nan_cols]
        else:
            _check_raw_data = data

        for (col, row) in _check_raw_data.isnull().any(axis=1).items():
            if row:
                print(f"存在缺失值: {col}, {_check_raw_data.loc[col]}")

        dumpfile = os.path.join(error_path, os.path.basename(file))
        pickle.dump(data, open(dumpfile, "wb"))
        raise Exception(f"文件存在缺失值 {dumpfile}")


def _read_time_point_data(file, cols=[], format_func=None, fillnan=False, need_check_nan_cols=[]):
    # 异常文件存储路径
    error_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(file))), "error"
    )

    # 检查数据列数 与列名是否匹配
    check_cols_num(file, error_path)

    # 进行必要的格式化
    # 字符串格式读取
    data = pd.read_csv(file, encoding="gbk", dtype=str)
    if len(cols) > 0:
        data = data.loc[:, cols]

    # 删除重复的行
    data.drop_duplicates(inplace=True)
    data.drop_duplicates(subset='时间', keep="last", inplace=True)

    # 删除 时间 列中 "时间未更新" 的行
    # 适配旧的历史数据
    data = data.query('时间 != "时间未更新"').reset_index(drop=True)

    # 格式化时间
    date = data["时间"][0][:10]
    data["时间"] = pd.to_datetime(data["时间"])
    data.set_index("时间", inplace=True)
    data = data.between_time("09:15:00", "15:00:00")

    # 格式化
    if format_func is not None:
        data = format_func(data)

    # 交易时间内的数据数量检查
    if len(data) == 0:
        return None

    # 标准化时间
    dts = get_time_point(date)
    dts["std_dts"] = 1

    # 按照合并 data dts
    data = pd.merge(data, dts, how="outer", left_index=True,
                    right_index=True, sort=True)

    # 对除了 std_dts 之外的列进行向后填充
    std_dts = data["std_dts"].copy()
    data = data.ffill()
    data["std_dts"] = std_dts

    # 标准时间的行
    data.query("std_dts == 1", inplace=True)

    # 删除 std_dts 列
    data.drop("std_dts", axis=1, inplace=True)

    # 全部转为 float
    data = data.astype(float)

    # 若有 09:25:00 的行，则填充 nan 为 0
    data.loc[data.index.time == datetime.time(9, 25)] = data.loc[data.index.time == datetime.time(9, 25)].fillna(0)

    # 检查是否有缺失的值
    check_nan_cols(file, data, error_path, fillnan, need_check_nan_cols)

    return data


def read_yddl(path, fillnan=False):
    # 读取标的概况
    # 进行必要的格式化

    file = path if path.endswith('.csv') else os.path.join(path, "一档队列.csv")

    def format_func(data):
        # 格式化数据
        data["卖均手"] = data["卖均手"].fillna("0/笔")
        data["卖均手"] = data["卖均手"].apply(lambda x: float(x.replace("/笔", "")))
        data["卖均手"] = data["卖均手"].astype(int)

        data["买均手"] = data["买均手"].fillna("0/笔")
        data["买均手"] = data["买均手"].apply(lambda x: float(x.replace("/笔", "")))
        data["买均手"] = data["买均手"].astype(int)

        data["卖总笔"] = data["卖总笔"].fillna("0笔")
        data["卖总笔"] = data["卖总笔"].apply(lambda x: float(x.replace("笔", "")))
        data["卖总笔"] = data["卖总笔"].astype(int)

        data["买总笔"] = data["买总笔"].fillna("0笔")
        data["买总笔"] = data["买总笔"].apply(lambda x: float(x.replace("笔", "")))
        data["买总笔"] = data["买总笔"].astype(int)

        data["卖价"] = data["卖价"].fillna(0)
        data["买价"] = data["买价"].fillna(0)

        return data

    return _read_time_point_data(file, [], format_func, fillnan)


def get_yddl_factor(path):
    # 获取数据
    return read_yddl(path)


def read_sdpk(path, fillnan=False, need_check_nan_cols=[]):
    # 读取十档盘口
    # 进行必要的格式化

    file = path if path.endswith('.csv') else os.path.join(path, "十档盘口.csv")

    for col in ['买1价', '卖1价']:
        if col not in need_check_nan_cols:
            need_check_nan_cols.append(col)
            
    def format_func(data):
        # 总卖
        try:
            data["总卖"] = data["总卖"].astype(float)
        except:
            data["总卖"] = data["总卖"].apply(
                lambda x: 10000 * (float(x.replace("万", "")))
                if "万" in str(x)
                else 1e8 * (float(x.replace("亿", "")))
                if "亿" in str(x)
                else float(x)
            )

        # 总买
        try:
            data["总买"] = data["总买"].astype(float)
        except:
            data["总买"] = data["总买"].apply(
                lambda x: 10000 * (float(x.replace("万", "")))
                if "万" in str(x)
                else 1e8 * (float(x.replace("亿", "")))
                if "亿" in str(x)
                else float(x)
            )

        return data

    return _read_time_point_data(file, [], format_func, fillnan, need_check_nan_cols)


def get_sdpk_factor(path):
    # 获取数据
    return read_sdpk(path)


def get_bdgk_factor(path):
    # 获取数据
    return read_bdgk(path)


if __name__ == "__main__":
    # factor = get_yddl_factor("data/his/163208/")
    # factor = get_bdgk_factor("data/his/163208/")
    factor = get_sdpk_factor("data/his/163208/")
