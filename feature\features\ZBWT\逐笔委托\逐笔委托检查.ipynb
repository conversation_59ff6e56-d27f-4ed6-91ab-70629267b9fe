{"cells": [{"cell_type": "code", "execution_count": 3, "id": "0fcee38d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "id": "445456b5", "metadata": {}, "source": ["# 原始数据"]}, {"cell_type": "code", "execution_count": 18, "id": "a9fce646", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "标识串及含义：\n", "B  限价买入                S  限价卖出\n", "1B 市价买入               1S 市价卖出\n", "UB 本方最优市价买入  US 本方最优市价卖出\n", "BC 撤买入单               SC 撤卖出单\n", "\"\"\"\n", "# 成交类型\n", "ORDER_TYPES = [\"B\", \"S\", \"BC\", \"SC\", \"1B\", \"1S\", \"UB\", \"US\"]\n", "B, S, BC, SC, _1B, _1S, UB, US = range(len(ORDER_TYPES))"]}, {"cell_type": "code", "execution_count": 7, "id": "f764aa66", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-07 09:15:00</th>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>1.056</td>\n", "      <td>1.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:15:00</th>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1.045</td>\n", "      <td>200.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:15:00</th>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>1.109</td>\n", "      <td>10.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:15:00</th>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>1.078</td>\n", "      <td>100.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:15:00</th>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>1.098</td>\n", "      <td>100.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 14:59:57</th>\n", "      <td>3</td>\n", "      <td>1h</td>\n", "      <td>1.059</td>\n", "      <td>1.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 14:59:57</th>\n", "      <td>4</td>\n", "      <td>1h</td>\n", "      <td>1.060</td>\n", "      <td>1.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 14:59:57</th>\n", "      <td>5</td>\n", "      <td>1h</td>\n", "      <td>1.061</td>\n", "      <td>1.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 14:59:58</th>\n", "      <td>1</td>\n", "      <td>16m</td>\n", "      <td>1.065</td>\n", "      <td>20.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 14:59:58</th>\n", "      <td>2</td>\n", "      <td>1h</td>\n", "      <td>1.068</td>\n", "      <td>820.0</td>\n", "      <td>SC</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>48083 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                     序号 在委时间     价格      手  类型\n", "时间                                            \n", "2023-09-07 09:15:00   1  NaN  1.056    1.0   S\n", "2023-09-07 09:15:00   2  NaN  1.045  200.0   B\n", "2023-09-07 09:15:00   3  NaN  1.109   10.0   S\n", "2023-09-07 09:15:00   4  NaN  1.078  100.0   S\n", "2023-09-07 09:15:00   5  NaN  1.098  100.0   S\n", "...                  ..  ...    ...    ...  ..\n", "2023-09-07 14:59:57   3   1h  1.059    1.0  BC\n", "2023-09-07 14:59:57   4   1h  1.060    1.0  BC\n", "2023-09-07 14:59:57   5   1h  1.061    1.0  BC\n", "2023-09-07 14:59:58   1  16m  1.065   20.0  BC\n", "2023-09-07 14:59:58   2   1h  1.068  820.0  SC\n", "\n", "[48083 rows x 5 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv('逐笔委托.csv', encoding='gbk')\n", "data['时间'] = pd.to_datetime(data['时间'])\n", "data = data.set_index('时间', drop=True)\n", "data"]}, {"cell_type": "code", "execution_count": 68, "id": "39d68aa8", "metadata": {}, "outputs": [], "source": ["# 检查范围\n", "begin = '09:30:00'\n", "end = '09:30:03'\n", "n = 10"]}, {"cell_type": "markdown", "id": "e888a6e5", "metadata": {}, "source": ["# 区间数据"]}, {"cell_type": "code", "execution_count": 9, "id": "cc63354f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>1</td>\n", "      <td>14m</td>\n", "      <td>1.054</td>\n", "      <td>5.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1.091</td>\n", "      <td>30.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>3</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>2.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>4</td>\n", "      <td>14m</td>\n", "      <td>1.041</td>\n", "      <td>21.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>5</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>10.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>79</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>1622.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>80</td>\n", "      <td>NaN</td>\n", "      <td>1.049</td>\n", "      <td>10.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>10.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>82</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>100.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>83</td>\n", "      <td>NaN</td>\n", "      <td>1.088</td>\n", "      <td>1101.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>363 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                     序号 在委时间     价格       手  类型\n", "时间                                             \n", "2023-09-07 09:30:00   1  14m  1.054     5.0  BC\n", "2023-09-07 09:30:00   2  NaN  1.091    30.0   S\n", "2023-09-07 09:30:00   3  14m  1.052     2.0  BC\n", "2023-09-07 09:30:00   4  14m  1.041    21.0  BC\n", "2023-09-07 09:30:00   5  14m  1.052    10.0  BC\n", "...                  ..  ...    ...     ...  ..\n", "2023-09-07 09:30:03  79  NaN  1.061  1622.0   B\n", "2023-09-07 09:30:03  80  NaN  1.049    10.0   B\n", "2023-09-07 09:30:03  81  NaN  1.064    10.0   S\n", "2023-09-07 09:30:03  82  NaN  1.064   100.0   S\n", "2023-09-07 09:30:03  83  NaN  1.088  1101.0   S\n", "\n", "[363 rows x 5 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 统计范围\n", "trades = data.between_time(begin, end)\n", "trades"]}, {"cell_type": "code", "execution_count": 20, "id": "d10346b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["363"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委托\n", "len(trades)"]}, {"cell_type": "code", "execution_count": 21, "id": "f11fea1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["73"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委买\n", "len(trades.query('类型 in [\"B\", \"1B\", \"UB\" ]'))"]}, {"cell_type": "code", "execution_count": 22, "id": "5118f2d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委买1\n", "len(trades.query('类型==\"1B\"'))"]}, {"cell_type": "code", "execution_count": 23, "id": "9766ba51", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委买优\n", "len(trades.query('类型==\"UB\"'))"]}, {"cell_type": "code", "execution_count": 24, "id": "4c8c6b04", "metadata": {}, "outputs": [{"data": {"text/plain": ["214"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委卖\n", "len(trades.query('类型 in [\"S\", \"1S\", \"US\" ]'))"]}, {"cell_type": "code", "execution_count": 26, "id": "972638f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委卖1\n", "len(trades.query('类型==\"1S\"'))"]}, {"cell_type": "code", "execution_count": 27, "id": "03692c19", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委卖优\n", "len(trades.query('类型==\"US\"'))"]}, {"cell_type": "code", "execution_count": 28, "id": "4f3c08de", "metadata": {}, "outputs": [{"data": {"text/plain": ["76"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委撤\n", "len(trades.query('类型 in [\"BC\", \"SC\"]'))"]}, {"cell_type": "code", "execution_count": 29, "id": "f70bf6c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["65"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委撤买\n", "len(trades.query('类型==\"BC\"'))"]}, {"cell_type": "code", "execution_count": 30, "id": "ccef1bc5", "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间委撤卖\n", "len(trades.query('类型==\"SC\"'))"]}, {"cell_type": "code", "execution_count": 32, "id": "72e700ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["159180"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间手数\n", "int(trades['手'].sum())"]}, {"cell_type": "code", "execution_count": 41, "id": "4ae1b775", "metadata": {}, "outputs": [{"data": {"text/plain": ["1186.1651531331863"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间手数波动率\n", "np.std(trades['手'])"]}, {"cell_type": "code", "execution_count": 34, "id": "aada2288", "metadata": {}, "outputs": [{"data": {"text/plain": ["39062"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间买手\n", "int(trades.query('类型 in [\"B\", \"1B\", \"UB\" ]')['手'].sum())"]}, {"cell_type": "code", "execution_count": 42, "id": "a21e026d", "metadata": {}, "outputs": [{"data": {"text/plain": ["1284.1366607210343"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间买手波动率\n", "np.std(trades.query('类型 in [\"B\", \"1B\", \"UB\" ]')['手'])"]}, {"cell_type": "code", "execution_count": 43, "id": "b943d433", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间买手1\n", "int(trades.query('类型 == \"1B\"')['手'].sum())"]}, {"cell_type": "code", "execution_count": 44, "id": "472e9df8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间买手优\n", "int(trades.query('类型 == \"UB\"')['手'].sum())"]}, {"cell_type": "code", "execution_count": 45, "id": "446aa69b", "metadata": {}, "outputs": [{"data": {"text/plain": ["92888"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间卖手\n", "int(trades.query('类型 in [\"S\", \"1S\", \"US\" ]')['手'].sum())"]}, {"cell_type": "code", "execution_count": 46, "id": "13fffa0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["1187.5008929206736"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间卖手波动率\n", "np.std(trades.query('类型 in [\"S\", \"1S\", \"US\" ]')['手'])"]}, {"cell_type": "code", "execution_count": 47, "id": "e37fb45f", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间卖手1\n", "int(trades.query('类型 == \"1S\"')['手'].sum())"]}, {"cell_type": "code", "execution_count": 48, "id": "8bbb8bf2", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间卖手优\n", "int(trades.query('类型 == \"US\"')['手'].sum())"]}, {"cell_type": "code", "execution_count": 50, "id": "390cb99d", "metadata": {}, "outputs": [{"data": {"text/plain": ["27230"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤手\n", "int(trades.query('类型 in [\"BC\", \"SC\"]')['手'].sum())"]}, {"cell_type": "code", "execution_count": 51, "id": "c30a714e", "metadata": {}, "outputs": [{"data": {"text/plain": ["17020"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤手买\n", "int(trades.query('类型 in [\"BC\"]')['手'].sum())"]}, {"cell_type": "code", "execution_count": 52, "id": "05d6feb1", "metadata": {}, "outputs": [{"data": {"text/plain": ["10210"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤手卖\n", "int(trades.query('类型 in [\"SC\"]')['手'].sum())"]}, {"cell_type": "code", "execution_count": 53, "id": "72092246", "metadata": {}, "outputs": [{"data": {"text/plain": ["28860.35999999999"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤额\n", "c_trades = trades.query('类型 in [\"BC\", \"SC\"]')\n", "(c_trades['手']*c_trades['价格']).sum()"]}, {"cell_type": "code", "execution_count": 54, "id": "f9dfaacf", "metadata": {}, "outputs": [{"data": {"text/plain": ["17782.441"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤额买\n", "c_trades = trades.query('类型 in [\"BC\"]')\n", "(c_trades['手']*c_trades['价格']).sum()"]}, {"cell_type": "code", "execution_count": 55, "id": "c87e954c", "metadata": {}, "outputs": [{"data": {"text/plain": ["11077.919"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["# ZBWT_区间撤额卖\n", "c_trades = trades.query('类型 in [\"SC\"]')\n", "(c_trades['手']*c_trades['价格']).sum()"]}, {"cell_type": "markdown", "id": "4a089b29", "metadata": {}, "source": ["# 累计数据"]}, {"cell_type": "code", "execution_count": 58, "id": "1fb9aab9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>1</td>\n", "      <td>14m</td>\n", "      <td>1.054</td>\n", "      <td>5.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1.091</td>\n", "      <td>30.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>3</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>2.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>4</td>\n", "      <td>14m</td>\n", "      <td>1.041</td>\n", "      <td>21.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>5</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>10.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>79</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>1622.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>80</td>\n", "      <td>NaN</td>\n", "      <td>1.049</td>\n", "      <td>10.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>10.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>82</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>100.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>83</td>\n", "      <td>NaN</td>\n", "      <td>1.088</td>\n", "      <td>1101.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>363 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                     序号 在委时间     价格       手  类型\n", "时间                                             \n", "2023-09-07 09:30:00   1  14m  1.054     5.0  BC\n", "2023-09-07 09:30:00   2  NaN  1.091    30.0   S\n", "2023-09-07 09:30:00   3  14m  1.052     2.0  BC\n", "2023-09-07 09:30:00   4  14m  1.041    21.0  BC\n", "2023-09-07 09:30:00   5  14m  1.052    10.0  BC\n", "...                  ..  ...    ...     ...  ..\n", "2023-09-07 09:30:03  79  NaN  1.061  1622.0   B\n", "2023-09-07 09:30:03  80  NaN  1.049    10.0   B\n", "2023-09-07 09:30:03  81  NaN  1.064    10.0   S\n", "2023-09-07 09:30:03  82  NaN  1.064   100.0   S\n", "2023-09-07 09:30:03  83  NaN  1.088  1101.0   S\n", "\n", "[363 rows x 5 columns]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["# 累计\n", "first = '09:30:00'\n", "cum_trade = data.between_time(first, end)\n", "cum_trade"]}, {"cell_type": "markdown", "id": "73927f89", "metadata": {}, "source": ["# 订单数据"]}, {"cell_type": "code", "execution_count": 60, "id": "3c80e68f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>1</td>\n", "      <td>14m</td>\n", "      <td>1.054</td>\n", "      <td>5.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1.091</td>\n", "      <td>30.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>3</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>2.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>4</td>\n", "      <td>14m</td>\n", "      <td>1.041</td>\n", "      <td>21.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:00</th>\n", "      <td>5</td>\n", "      <td>14m</td>\n", "      <td>1.052</td>\n", "      <td>10.0</td>\n", "      <td>BC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>79</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>1622.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>80</td>\n", "      <td>NaN</td>\n", "      <td>1.049</td>\n", "      <td>10.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>10.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>82</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>100.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 09:30:03</th>\n", "      <td>83</td>\n", "      <td>NaN</td>\n", "      <td>1.088</td>\n", "      <td>1101.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>363 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                     序号 在委时间     价格       手  类型\n", "时间                                             \n", "2023-09-07 09:30:00   1  14m  1.054     5.0  BC\n", "2023-09-07 09:30:00   2  NaN  1.091    30.0   S\n", "2023-09-07 09:30:00   3  14m  1.052     2.0  BC\n", "2023-09-07 09:30:00   4  14m  1.041    21.0  BC\n", "2023-09-07 09:30:00   5  14m  1.052    10.0  BC\n", "...                  ..  ...    ...     ...  ..\n", "2023-09-07 09:30:03  79  NaN  1.061  1622.0   B\n", "2023-09-07 09:30:03  80  NaN  1.049    10.0   B\n", "2023-09-07 09:30:03  81  NaN  1.064    10.0   S\n", "2023-09-07 09:30:03  82  NaN  1.064   100.0   S\n", "2023-09-07 09:30:03  83  NaN  1.088  1101.0   S\n", "\n", "[363 rows x 5 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["time_data = trades.copy()\n", "time_data"]}, {"cell_type": "code", "execution_count": 71, "id": "98764f6a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>28</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>4995.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>30</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>4995.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>55</td>\n", "      <td>NaN</td>\n", "      <td>1.059</td>\n", "      <td>4995.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>1.058</td>\n", "      <td>4995.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>92</td>\n", "      <td>NaN</td>\n", "      <td>1.056</td>\n", "      <td>4995.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>10</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>3000.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>79</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>1622.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>1.058</td>\n", "      <td>1000.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>29</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>879.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "      <td>1.061</td>\n", "      <td>817.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   时间   序号 在委时间     价格       手 类型\n", "0 2023-09-07 09:30:00   28  NaN  1.061  4995.0  B\n", "1 2023-09-07 09:30:00   30  NaN  1.061  4995.0  B\n", "2 2023-09-07 09:30:00   55  NaN  1.059  4995.0  B\n", "3 2023-09-07 09:30:01    3  NaN  1.058  4995.0  B\n", "4 2023-09-07 09:30:01   92  NaN  1.056  4995.0  B\n", "5 2023-09-07 09:30:03   10  NaN  1.063  3000.0  B\n", "6 2023-09-07 09:30:03   79  NaN  1.061  1622.0  B\n", "7 2023-09-07 09:30:02  100  NaN  1.058  1000.0  B\n", "8 2023-09-07 09:30:01   29  NaN  1.063   879.0  B\n", "9 2023-09-07 09:30:03    6  NaN  1.061   817.0  B"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["time_data.query('类型 in [\"B\", \"1B\", \"UB\" ]').sort_values(['手', '价格'], ascending=[False, False]).iloc[:n,:].reset_index()"]}, {"cell_type": "code", "execution_count": 73, "id": "c0ba59d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>44</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>53</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>100.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>10</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>3000.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>29</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>879.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>107</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>287.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>37</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>100.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>11</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>50.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>75</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>50.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>88</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>20.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>35</td>\n", "      <td>NaN</td>\n", "      <td>1.063</td>\n", "      <td>1.0</td>\n", "      <td>B</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   时间   序号 在委时间     价格       手 类型\n", "0 2023-09-07 09:30:00   44  NaN  1.064   200.0  B\n", "1 2023-09-07 09:30:00   53  NaN  1.064   100.0  B\n", "2 2023-09-07 09:30:03   10  NaN  1.063  3000.0  B\n", "3 2023-09-07 09:30:01   29  NaN  1.063   879.0  B\n", "4 2023-09-07 09:30:02  107  NaN  1.063   287.0  B\n", "5 2023-09-07 09:30:01   37  NaN  1.063   100.0  B\n", "6 2023-09-07 09:30:03   11  NaN  1.063    50.0  B\n", "7 2023-09-07 09:30:03   75  NaN  1.063    50.0  B\n", "8 2023-09-07 09:30:01   88  NaN  1.063    20.0  B\n", "9 2023-09-07 09:30:03   35  NaN  1.063     1.0  B"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["time_data.query('类型 in [\"B\", \"1B\", \"UB\" ]').sort_values(['价格', '手'], ascending=[False, False]).iloc[:n,:].reset_index()"]}, {"cell_type": "code", "execution_count": 74, "id": "2c0ef8fb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>15</td>\n", "      <td>NaN</td>\n", "      <td>1.065</td>\n", "      <td>10000.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>66</td>\n", "      <td>NaN</td>\n", "      <td>1.065</td>\n", "      <td>6000.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>65</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>5000.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>29</td>\n", "      <td>NaN</td>\n", "      <td>1.067</td>\n", "      <td>4995.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>31</td>\n", "      <td>NaN</td>\n", "      <td>1.070</td>\n", "      <td>4995.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-09-07 09:30:00</td>\n", "      <td>60</td>\n", "      <td>NaN</td>\n", "      <td>1.071</td>\n", "      <td>4995.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>31</td>\n", "      <td>NaN</td>\n", "      <td>1.073</td>\n", "      <td>4995.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>40</td>\n", "      <td>NaN</td>\n", "      <td>1.073</td>\n", "      <td>4995.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>4151.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>39</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>3407.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   时间  序号 在委时间     价格        手 类型\n", "0 2023-09-07 09:30:01  15  NaN  1.065  10000.0  S\n", "1 2023-09-07 09:30:02  66  NaN  1.065   6000.0  S\n", "2 2023-09-07 09:30:02  65  NaN  1.064   5000.0  S\n", "3 2023-09-07 09:30:00  29  NaN  1.067   4995.0  S\n", "4 2023-09-07 09:30:00  31  NaN  1.070   4995.0  S\n", "5 2023-09-07 09:30:00  60  NaN  1.071   4995.0  S\n", "6 2023-09-07 09:30:01  31  NaN  1.073   4995.0  S\n", "7 2023-09-07 09:30:02  40  NaN  1.073   4995.0  S\n", "8 2023-09-07 09:30:01   5  NaN  1.064   4151.0  S\n", "9 2023-09-07 09:30:02  39  NaN  1.064   3407.0  S"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["time_data.query('类型 in [\"S\", \"1S\", \"US\" ]').sort_values(['手', '价格'], ascending=[False, True]).iloc[:n,:].reset_index()"]}, {"cell_type": "code", "execution_count": 75, "id": "df32119b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>在委时间</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>65</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>5000.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>4151.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>39</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>3407.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>55</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>1800.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>18</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>1717.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>1000.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-09-07 09:30:03</td>\n", "      <td>67</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>842.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>22</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>500.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-07 09:30:02</td>\n", "      <td>14</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>500.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-09-07 09:30:01</td>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>1.064</td>\n", "      <td>245.0</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   时间  序号 在委时间     价格       手 类型\n", "0 2023-09-07 09:30:02  65  NaN  1.064  5000.0  S\n", "1 2023-09-07 09:30:01   5  NaN  1.064  4151.0  S\n", "2 2023-09-07 09:30:02  39  NaN  1.064  3407.0  S\n", "3 2023-09-07 09:30:03  55  NaN  1.064  1800.0  S\n", "4 2023-09-07 09:30:02  18  NaN  1.064  1717.0  S\n", "5 2023-09-07 09:30:02   4  NaN  1.064  1000.0  S\n", "6 2023-09-07 09:30:03  67  NaN  1.064   842.0  S\n", "7 2023-09-07 09:30:01  22  NaN  1.064   500.0  S\n", "8 2023-09-07 09:30:02  14  NaN  1.064   500.0  S\n", "9 2023-09-07 09:30:01  81  NaN  1.064   245.0  S"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["time_data.query('类型 in [\"S\", \"1S\", \"US\" ]').sort_values(['价格', '手'], ascending=[True, False]).iloc[:n,:].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "90b2543b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6be0c6e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}