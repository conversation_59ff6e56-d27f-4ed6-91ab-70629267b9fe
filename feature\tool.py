import pytz
import socket
import pandas as pd
import numpy as np
import datetime
import os, pickle

from py_ext.wechat import send_wx

from .config import fee_rate, min_fee
from .config import get_realtime_data_path, get_his_data_path, get_his_times_binance, get_his_dates, get_realtime_dates
# 创建北京时区对象
tz_beijing = pytz.timezone('Asia/Shanghai')

########################################
# 获取数据
###############################


def get_dates(begin: str, end: str, his: bool):
    # 获取对应范围内的数据日期列表
    # a股市场
    dates = get_his_dates() if his else get_realtime_dates()
    dates = [i for i in dates if begin <= i <= end]
    return dates


def get_times_digital_binance(begin: str, end: str):
    # 获取对应范围内的数据日期列表
    # 数字货币 binance

    # begin end 转为时间戳
    begin = tz_beijing.localize(datetime.datetime.strptime(
        begin, "%Y%m%d")).timestamp() * 1000
    end = tz_beijing.localize(datetime.datetime.strptime(
        end, "%Y%m%d")).timestamp() * 1000

    times = get_his_times_binance()
    times = [i for i in times if begin <= i <= end]
    return times


def get_his_raw_data(code, date, raw_name, raw_check_func=None):
    """
    获取历史储存的原始数据
    code: 513050
    date: 20230901
    raw_name: raw文件名称: 逐笔成交/逐笔委托/标的概况/十档盘口/一档队列
    raw_check_func: 检查原始数据函数

    成功返回dataframe
    失败返回None
    """
    if raw_name not in ['逐笔成交', '逐笔委托', '标的概况', '十档盘口', '一档队列']:
        raise Exception(
            'raw_name:{raw_name} 无效,应该为下列其一:\n逐笔成交/逐笔委托/标的概况/十档盘口/一档队列')

    file = get_his_data_path(code, date, f'{raw_name}.csv')
    if not file:
        return None

    if not None is raw_check_func:
        file = raw_check_func(file)
        # 文件异常
        if not file:
            return None

    try:
        data = pd.read_csv(file, encoding="gbk", dtype=str)
    except Exception as e:
        msg = f'{file} 读取失败\n{e}'
        print(msg)
        send_wx(msg)
        return None

    # 只有一行标题行
    if len(data) <= 1:
        return None

    return data

###################################
# 数据处理
###################################


def drop_duplicate_row(data):
    """
    删除重复行，保留第一个数据
    """
    data.drop_duplicates(inplace=True)
    return data


def balance_label(data, label_name='target'):
    # 降采样均衡样本
    # 最小标签样本数量
    # 返回 data
    label_min = data[label_name].value_counts().min()

    # 标签类型
    labels = data[label_name].unique()

    idxs = []
    for i in labels:
        _idx = data[data[label_name] == i].index.to_list()

        if len(_idx) <= label_min:
            # 如果数量相等，不做处理
            idxs.extend(_idx)
        else:
            # 如果数量超出，随机抽取
            idxs.extend(np.random.choice(_idx, label_min, replace=False))

    # 降采样
    data = data.loc[idxs, :]

    return data


def balance_label_x_y(X_train, y_train, labels=[0, 1]):
    # 降采样均衡样本
    # 最小标签样本数量
    # 返回 X_train, y_train
    label_min = y_train.value_counts().min()

    idxs = []
    for i in labels:
        _idx = y_train[y_train == i].index.to_list()

        if len(_idx) <= label_min:
            # 如果数量相等，不做处理
            idxs.extend(_idx)
        else:
            # 如果数量超出，随机抽取
            idxs.extend(np.random.choice(_idx, label_min, replace=False))

    # 降采样
    X_train = X_train.loc[idxs, :]
    y_train = y_train.loc[idxs]

    return X_train, y_train


def get_price_3s_bdgk(date, code, need_cols=['现价']):
    """
    通过 标的概况 获取3s切片标准的价格数据
    时间|现价(默认)
    """
    file = f"D:/通达信录制数据/his_data/{date.replace('-', '')}/{code}/标的概况.csv"
    if not os.path.exists(file):
        return None

    bdgk = pd.read_csv(file, encoding='gbk')
    bdgk['时间'] = pd.to_datetime(bdgk['时间'])
    bdgk.set_index('时间', inplace=True, drop=True)
    bdgk = std_3s_time_data(bdgk)
    bdgk = bdgk.loc[:, need_cols]
    return bdgk


def get_trade_fee(amount):
    """
    交易成本
    """
    return 2 * min(min_fee, round(amount * fee_rate, 2))


def get_time_point(date=""):
    """
    返回 09:30:03-11:30 13:00:03-14:56:57 期间内每三秒的时间序列
    """
    # 定义起始时间和结束时间
    if date == "":
        date = datetime.datetime.now().astimezone(tz_beijing).strftime("%Y-%m-%d ")
    else:
        # 统一时间格式
        if '-' not in date:
            date = date[:4] + '-' + date[4:6] + '-' + date[6:]

        date = date + " "

    start_time = date + "09:30:00"
    end_time = date + "14:56:57"

    start_time_1 = date + "11:30:00"
    end_time_1 = date + "13:00:00"

    # 生成时间序列
    dts = pd.date_range(start=start_time, end=end_time, freq="3s")

    # dts = dts[~((dts > start_time_1) & (dts <= end_time_1))]
    # 需要包含 11:30:00/13:00:00 通达信存在数据，且与相邻价格不一样
    dts = dts[~((dts > start_time_1) & (dts < end_time_1))]
    # 需要包含 09:30:00 通达信存在数据，且与开盘价不一样
    # dts = dts[1:]

    # 添加 09:25:00 开盘价
    _dt = pd.Timestamp(date + ' 09:25:00')
    dts = dts.insert(0, _dt)

    # 转为dataframe
    return pd.DataFrame(dts, columns=["时间"]).set_index("时间", drop=True)


def top_to_cols(_trade, _type_name, sort_by, dt, n, if_trade=True):
    """
    将筛选出来的订单/成交 转成 一行
    _trade: 订单/成交
    _type_name: 买/卖 用于列命名
    sort_by: 排序列名 用于列命名
    dt: 所属的时间索引
    n: top n
    """
    _trade.reset_index(drop=True, inplace=True)

    # 添加到足够的数量
    if len(_trade) < n:
        _trade = pd.concat(
            [pd.DataFrame(range(n)), _trade], axis=1).iloc[:, 1:]
        _trade.fillna(-1, inplace=True)

    sort_by_var_name = '量' if sort_by[0][0] == '手' else '价'

    # 转置到一行中
    p = _trade[['价格']].T.reset_index(drop=True)
    p.columns = [
        f'{"委" if not if_trade else "成"}{_type_name + sort_by_var_name}top{i+1}价' for i in range(n)]
    v = _trade[['手']].T.reset_index(drop=True)
    v.columns = [
        f'{"委" if not if_trade else "成"}{_type_name + sort_by_var_name}top{i+1}手' for i in range(n)]

    # 合并
    _top_deal = pd.concat([p, v], axis=1)

    # 排序
    columns = list(_top_deal)
    fix_columns = []
    for i in range(n):
        fix_columns.append(columns[i])
        fix_columns.append(columns[i+n])

    _top_deal = _top_deal[fix_columns]

    # 统一时间索引
    _top_deal['时间'] = dt
    _top_deal.set_index('时间', inplace=True)

    return _top_deal


def std_3s_time_data(data, dropna=False):
    """
    data: 索引为时间
    返回标准话成3s切片的dataframe
    """
    # assert 没有如何nan值
    if not (not data.isnull().values.any() and not np.isinf(data).values.any()):
        pickle.dump(data, open("has_nan_data", "wb"))
        raise Exception("data不能有nan值")

    # 2023-01-01
    date = str(data.index[0])[:10]

    # 标准化时间
    dts = get_time_point(date)
    dts["std_dts"] = 1

    # 按照合并 data dts
    data = pd.merge(data, dts, how="outer", left_index=True,
                    right_index=True, sort=True)

    # 对除了 std_dts 之外的列进行向后填充
    std_dts = data["std_dts"].copy()
    # data.fillna(method="ffill", inplace=True,
    #             limit=(None if not dropna else 1))
    data.ffill(inplace=True,limit=(None if not dropna else 1))
    data["std_dts"] = std_dts

    # 标准时间的行
    data.query("std_dts == 1", inplace=True)

    # 删除 std_dts 列
    data.drop("std_dts", axis=1, inplace=True)

    if dropna:
        data.dropna(inplace=True)

    return data


# 其他工具


def is_port_open(host, port):
    # 创建一个套接字对象
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    # 设置超时时间为1秒
    sock.settimeout(1)

    try:
        # 尝试连接到指定的主机和端口
        result = sock.connect_ex((host, port))
        if result == 0:
            # 端口可用
            return True
        else:
            # 端口不可用
            return False
    except socket.error as e:
        # 连接出现异常，端口不可用
        return False
    finally:
        # 关闭套接字连接
        sock.close()


if __name__ == "__main__":
    dts = get_time_point()
    pass
