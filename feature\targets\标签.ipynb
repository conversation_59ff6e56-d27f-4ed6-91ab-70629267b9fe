{"cells": [{"cell_type": "code", "execution_count": 1, "id": "95826fee", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "from tool import get_time_point\n", "from bdgk import get_bdgk_factor_his"]}, {"cell_type": "code", "execution_count": 2, "id": "8a9ab701", "metadata": {}, "outputs": [], "source": ["# n 秒后的收益率\n", "n = 30"]}, {"cell_type": "code", "execution_count": 3, "id": "97d33103", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>内外盘差</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>-7823.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>10893.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>4483.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>-11254.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.33</td>\n", "      <td>90665.0</td>\n", "      <td>9648000.0</td>\n", "      <td>501.0</td>\n", "      <td>181.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>-11254.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3441000.0</td>\n", "      <td>366000000.0</td>\n", "      <td>13212.0</td>\n", "      <td>260.5</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>517000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>520000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>521000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>520000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>520000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:12  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:15  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:48  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "2023-09-01 15:00:00  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:12  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:15  0.19  2.33    90665.0    9648000.0    501.0  181.0   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:48  0.85  0.37  3441000.0  366000000.0  13212.0  260.5   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "2023-09-01 15:00:00  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "\n", "                        涨停     跌停      内外盘差  \n", "时间                                           \n", "2023-09-01 09:30:03  1.163  0.951   -7823.0  \n", "2023-09-01 09:30:06  1.163  0.951   10893.0  \n", "2023-09-01 09:30:09  1.163  0.951    4483.0  \n", "2023-09-01 09:30:12  1.163  0.951  -11254.0  \n", "2023-09-01 09:30:15  1.163  0.951  -11254.0  \n", "...                    ...    ...       ...  \n", "2023-09-01 14:59:48  1.163  0.951  517000.0  \n", "2023-09-01 14:59:51  1.163  0.951  520000.0  \n", "2023-09-01 14:59:54  1.163  0.951  521000.0  \n", "2023-09-01 14:59:57  1.163  0.951  520000.0  \n", "2023-09-01 15:00:00  1.163  0.951  520000.0  \n", "\n", "[4800 rows x 17 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["bdgk = get_bdgk_factor_his('20230901', '513050')\n", "bdgk"]}, {"cell_type": "code", "execution_count": 4, "id": "97c08377", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03    1.063\n", "2023-09-01 09:30:06    1.064\n", "2023-09-01 09:30:09    1.063\n", "2023-09-01 09:30:12    1.063\n", "2023-09-01 09:30:15    1.064\n", "                       ...  \n", "2023-09-01 14:59:48    1.067\n", "2023-09-01 14:59:51    1.067\n", "2023-09-01 14:59:54    1.067\n", "2023-09-01 14:59:57    1.066\n", "2023-09-01 15:00:00    1.066\n", "Name: 现价, Length: 4800, dtype: float64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["price_data = bdgk['现价']\n", "price_data"]}, {"cell_type": "code", "execution_count": 5, "id": "3c9c9e54", "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["n_shift = n//3\n", "n_shift"]}, {"cell_type": "code", "execution_count": 6, "id": "c7ef3927", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>现价</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:30:03</td>\n", "      <td>1.063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:30:06</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:30:09</td>\n", "      <td>1.063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:30:12</td>\n", "      <td>1.063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:30:15</td>\n", "      <td>1.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4795</th>\n", "      <td>2023-09-01 14:59:48</td>\n", "      <td>1.067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4796</th>\n", "      <td>2023-09-01 14:59:51</td>\n", "      <td>1.067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4797</th>\n", "      <td>2023-09-01 14:59:54</td>\n", "      <td>1.067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4798</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>1.066</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4799</th>\n", "      <td>2023-09-01 15:00:00</td>\n", "      <td>1.066</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                      时间     现价\n", "0    2023-09-01 09:30:03  1.063\n", "1    2023-09-01 09:30:06  1.064\n", "2    2023-09-01 09:30:09  1.063\n", "3    2023-09-01 09:30:12  1.063\n", "4    2023-09-01 09:30:15  1.064\n", "...                  ...    ...\n", "4795 2023-09-01 14:59:48  1.067\n", "4796 2023-09-01 14:59:51  1.067\n", "4797 2023-09-01 14:59:54  1.067\n", "4798 2023-09-01 14:59:57  1.066\n", "4799 2023-09-01 15:00:00  1.066\n", "\n", "[4800 rows x 2 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["price_data = pd.DataFrame(price_data).reset_index()\n", "price_data"]}, {"cell_type": "code", "execution_count": 12, "id": "a019714f", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.06299984"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["1.064*(1-0.000940)"]}, {"cell_type": "code", "execution_count": 17, "id": "d525be71", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0630003099279446"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["import math\n", "1.064 * math.exp(-0.000940)"]}, {"cell_type": "code", "execution_count": 11, "id": "800ba733", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>现价</th>\n", "      <th>时间2</th>\n", "      <th>现价2</th>\n", "      <th>ret</th>\n", "      <th>ret_log</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:30:03</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:30:33</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:30:06</td>\n", "      <td>1.064</td>\n", "      <td>2023-09-01 09:30:36</td>\n", "      <td>1.063</td>\n", "      <td>-0.000940</td>\n", "      <td>-0.000940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:30:09</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:30:39</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:30:12</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:30:42</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:30:15</td>\n", "      <td>1.064</td>\n", "      <td>2023-09-01 09:30:45</td>\n", "      <td>1.063</td>\n", "      <td>-0.000940</td>\n", "      <td>-0.000940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-09-01 09:30:18</td>\n", "      <td>1.064</td>\n", "      <td>2023-09-01 09:30:48</td>\n", "      <td>1.063</td>\n", "      <td>-0.000940</td>\n", "      <td>-0.000940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-09-01 09:30:21</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:30:51</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-09-01 09:30:24</td>\n", "      <td>1.064</td>\n", "      <td>2023-09-01 09:30:54</td>\n", "      <td>1.063</td>\n", "      <td>-0.000940</td>\n", "      <td>-0.000940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-01 09:30:27</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:30:57</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-09-01 09:30:30</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:00</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-09-01 09:30:33</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:03</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-09-01 09:30:36</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:06</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-09-01 09:30:39</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:09</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-09-01 09:30:42</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:12</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-09-01 09:30:45</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:15</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-09-01 09:30:48</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:18</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-09-01 09:30:51</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:21</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-09-01 09:30:54</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:24</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-09-01 09:30:57</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:27</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-09-01 09:31:00</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:30</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2023-09-01 09:31:03</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:33</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2023-09-01 09:31:06</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:31:36</td>\n", "      <td>1.062</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2023-09-01 09:31:09</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:39</td>\n", "      <td>1.062</td>\n", "      <td>-0.000941</td>\n", "      <td>-0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2023-09-01 09:31:12</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:42</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2023-09-01 09:31:15</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:31:45</td>\n", "      <td>1.063</td>\n", "      <td>0.000942</td>\n", "      <td>0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2023-09-01 09:31:18</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:31:48</td>\n", "      <td>1.062</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2023-09-01 09:31:21</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:31:51</td>\n", "      <td>1.063</td>\n", "      <td>0.000942</td>\n", "      <td>0.000941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2023-09-01 09:31:24</td>\n", "      <td>1.063</td>\n", "      <td>2023-09-01 09:31:54</td>\n", "      <td>1.063</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2023-09-01 09:31:27</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:31:57</td>\n", "      <td>1.062</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2023-09-01 09:31:30</td>\n", "      <td>1.062</td>\n", "      <td>2023-09-01 09:32:00</td>\n", "      <td>1.063</td>\n", "      <td>0.000942</td>\n", "      <td>0.000941</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    时间     现价                 时间2    现价2       ret   ret_log\n", "0  2023-09-01 09:30:03  1.063 2023-09-01 09:30:33  1.063  0.000000  0.000000\n", "1  2023-09-01 09:30:06  1.064 2023-09-01 09:30:36  1.063 -0.000940 -0.000940\n", "2  2023-09-01 09:30:09  1.063 2023-09-01 09:30:39  1.063  0.000000  0.000000\n", "3  2023-09-01 09:30:12  1.063 2023-09-01 09:30:42  1.063  0.000000  0.000000\n", "4  2023-09-01 09:30:15  1.064 2023-09-01 09:30:45  1.063 -0.000940 -0.000940\n", "5  2023-09-01 09:30:18  1.064 2023-09-01 09:30:48  1.063 -0.000940 -0.000940\n", "6  2023-09-01 09:30:21  1.063 2023-09-01 09:30:51  1.063  0.000000  0.000000\n", "7  2023-09-01 09:30:24  1.064 2023-09-01 09:30:54  1.063 -0.000940 -0.000940\n", "8  2023-09-01 09:30:27  1.063 2023-09-01 09:30:57  1.063  0.000000  0.000000\n", "9  2023-09-01 09:30:30  1.063 2023-09-01 09:31:00  1.063  0.000000  0.000000\n", "10 2023-09-01 09:30:33  1.063 2023-09-01 09:31:03  1.063  0.000000  0.000000\n", "11 2023-09-01 09:30:36  1.063 2023-09-01 09:31:06  1.062 -0.000941 -0.000941\n", "12 2023-09-01 09:30:39  1.063 2023-09-01 09:31:09  1.063  0.000000  0.000000\n", "13 2023-09-01 09:30:42  1.063 2023-09-01 09:31:12  1.063  0.000000  0.000000\n", "14 2023-09-01 09:30:45  1.063 2023-09-01 09:31:15  1.062 -0.000941 -0.000941\n", "15 2023-09-01 09:30:48  1.063 2023-09-01 09:31:18  1.062 -0.000941 -0.000941\n", "16 2023-09-01 09:30:51  1.063 2023-09-01 09:31:21  1.062 -0.000941 -0.000941\n", "17 2023-09-01 09:30:54  1.063 2023-09-01 09:31:24  1.063  0.000000  0.000000\n", "18 2023-09-01 09:30:57  1.063 2023-09-01 09:31:27  1.062 -0.000941 -0.000941\n", "19 2023-09-01 09:31:00  1.063 2023-09-01 09:31:30  1.062 -0.000941 -0.000941\n", "20 2023-09-01 09:31:03  1.063 2023-09-01 09:31:33  1.063  0.000000  0.000000\n", "21 2023-09-01 09:31:06  1.062 2023-09-01 09:31:36  1.062  0.000000  0.000000\n", "22 2023-09-01 09:31:09  1.063 2023-09-01 09:31:39  1.062 -0.000941 -0.000941\n", "23 2023-09-01 09:31:12  1.063 2023-09-01 09:31:42  1.063  0.000000  0.000000\n", "24 2023-09-01 09:31:15  1.062 2023-09-01 09:31:45  1.063  0.000942  0.000941\n", "25 2023-09-01 09:31:18  1.062 2023-09-01 09:31:48  1.062  0.000000  0.000000\n", "26 2023-09-01 09:31:21  1.062 2023-09-01 09:31:51  1.063  0.000942  0.000941\n", "27 2023-09-01 09:31:24  1.063 2023-09-01 09:31:54  1.063  0.000000  0.000000\n", "28 2023-09-01 09:31:27  1.062 2023-09-01 09:31:57  1.062  0.000000  0.000000\n", "29 2023-09-01 09:31:30  1.062 2023-09-01 09:32:00  1.063  0.000942  0.000941"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["price_data[['时间2', '现价2']] = price_data[['时间', '现价']].shift(-n_shift)\n", "price_data.eval('ret=(现价2/现价)-1', inplace=True)\n", "price_data['ret_log'] = np.log(price_data['现价2']/price_data['现价'])\n", "price_data.head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "2750c240", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "id": "664e61f7", "metadata": {}, "outputs": [], "source": ["# 交易成本\n", "tax = 0.00005"]}, {"cell_type": "code", "execution_count": 29, "id": "1762dc78", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.11"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["amount = 2277.99\n", "round(amount * tax, 2)"]}, {"cell_type": "code", "execution_count": null, "id": "2d520201", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "76bc45ad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4b3ead07", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fcf864e7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4b46c142", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f1fd480", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}