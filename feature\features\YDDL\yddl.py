import pandas as pd
import numpy as np

from ...base_class import time_point_feature


class yddl_001(time_point_feature):
    """
    1 + 106

    '时间' +
    ['卖价', '卖均手', '卖总笔', '卖1', '卖2', '卖3', '卖4', '卖5', '卖6', '卖7', '卖8', '卖9', '卖10', '卖11', '卖12', '卖13', '卖14', '卖15', '卖16', '卖17', '卖18', '卖19', '卖20', '卖21', '卖22', '卖23', '卖24', '卖25', '卖26', '卖27', '卖28', '卖29', '卖30', '卖31', '卖32', '卖33', '卖34', '卖35', '卖36', '卖37', '卖38', '卖39', '卖40', '卖41', '卖42', '卖43', '卖44', '卖45', '卖46', '卖47', '卖48', '卖49', '卖50', '买价', '买均手', '买总笔', '买1', '买2', '买3', '买4', '买5', '买6', '买7', '买8', '买9', '买10', '买11', '买12', '买13', '买14', '买15', '买16', '买17', '买18', '买19', '买20', '买21', '买22', '买23', '买24', '买25', '买26', '买27', '买28', '买29', '买30', '买31', '买32', '买33', '买34', '买35', '买36', '买37', '买38', '买39', '买40', '买41', '买42', '买43', '买44', '买45', '买46', '买47', '买48', '买49', '买50']
    """

    def need_raw_cols(self):
        return [
            '时间',
            '卖价',
            '卖均手',
            '卖总笔',
            '卖1',
            '卖2',
            '卖3',
            '卖4',
            '卖5',
            '卖6',
            '卖7',
            '卖8',
            '卖9',
            '卖10',
            # '卖11',
            # '卖12',
            # '卖13',
            # '卖14',
            # '卖15',
            # '卖16',
            # '卖17',
            # '卖18',
            # '卖19',
            # '卖20',
            # '卖21',
            # '卖22',
            # '卖23',
            # '卖24',
            # '卖25',
            # '卖26',
            # '卖27',
            # '卖28',
            # '卖29',
            # '卖30',
            # '卖31',
            # '卖32',
            # '卖33',
            # '卖34',
            # '卖35',
            # '卖36',
            # '卖37',
            # '卖38',
            # '卖39',
            # '卖40',
            # '卖41',
            # '卖42',
            # '卖43',
            # '卖44',
            # '卖45',
            # '卖46',
            # '卖47',
            # '卖48',
            # '卖49',
            # '卖50',
            # '卖51',
            # '卖52',
            # '卖53',
            # '卖54',
            # '卖55',
            # '卖56',
            # '卖57',
            # '卖58',
            # '卖59',
            # '卖60',
            # '卖61',
            # '卖62',
            # '卖63',
            # '卖64',
            # '卖65',
            # '卖66',
            # '卖67',
            # '卖68',
            # '卖69',
            # '卖70',
            # '卖71',
            # '卖72',
            # '卖73',
            # '卖74',
            # '卖75',
            # '卖76',
            # '卖77',
            # '卖78',
            # '卖79',
            # '卖80',
            # '卖81',
            # '卖82',
            # '卖83',
            # '卖84',
            # '卖85',
            # '卖86',
            # '卖87',
            # '卖88',
            # '卖89',
            # '卖90',
            # '卖91',
            # '卖92',
            # '卖93',
            # '卖94',
            # '卖95',
            # '卖96',
            # '卖97',
            # '卖98',
            # '卖99',
            # '卖100',
            '买价',
            '买均手',
            '买总笔',
            '买1',
            '买2',
            '买3',
            '买4',
            '买5',
            '买6',
            '买7',
            '买8',
            '买9',
            '买10',
            # '买11',
            # '买12',
            # '买13',
            # '买14',
            # '买15',
            # '买16',
            # '买17',
            # '买18',
            # '买19',
            # '买20',
            # '买21',
            # '买22',
            # '买23',
            # '买24',
            # '买25',
            # '买26',
            # '买27',
            # '买28',
            # '买29',
            # '买30',
            # '买31',
            # '买32',
            # '买33',
            # '买34',
            # '买35',
            # '买36',
            # '买37',
            # '买38',
            # '买39',
            # '买40',
            # '买41',
            # '买42',
            # '买43',
            # '买44',
            # '买45',
            # '买46',
            # '买47',
            # '买48',
            # '买49',
            # '买50',
            # '买51',
            # '买52',
            # '买53',
            # '买54',
            # '买55',
            # '买56',
            # '买57',
            # '买58',
            # '买59',
            # '买60',
            # '买61',
            # '买62',
            # '买63',
            # '买64',
            # '买65',
            # '买66',
            # '买67',
            # '买68',
            # '买69',
            # '买70',
            # '买71',
            # '买72',
            # '买73',
            # '买74',
            # '买75',
            # '买76',
            # '买77',
            # '买78',
            # '买79',
            # '买80',
            # '买81',
            # '买82',
            # '买83',
            # '买84',
            # '买85',
            # '买86',
            # '买87',
            # '买88',
            # '买89',
            # '买90',
            # '买91',
            # '买92',
            # '买93',
            # '买94',
            # '买95',
            # '买96',
            # '买97',
            # '买98',
            # '买99',
            # '买100'
        ]

    def feature_check(self, data: pd.DataFrame):
        return True

    def raw_name(self):
        return "一档队列"

    def version(self):
        return "001_20230911"

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """
        # /笔处理
        unit_cols_0 = ['卖均手', '买均手']
        data.loc[:, unit_cols_0] = data.loc[:,
                                            unit_cols_0].replace("/笔", "", regex=True)

        # 笔处理
        unit_cols_1 = ['卖总笔', '买总笔']
        data.loc[:, unit_cols_1] = data.loc[:,
                                            unit_cols_1].replace("笔", "", regex=True)

        return data.fillna(0)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 添加前缀
        data.columns = [f'YDDL_{i}' for i in list(data)]
        return data

    def name(self):
        """
        返回特征名称
        """
        return "一档队列"
