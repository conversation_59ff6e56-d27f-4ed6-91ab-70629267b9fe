import pandas as pd
import numpy as np

def test_skew_zero_cases():
    """测试偏度可能为0的情况"""
    
    print("=== 测试偏度可能为0的情况 ===\n")
    
    # 情况1: 所有价格都相同
    print("情况1: 所有卖价都相同")
    df1 = pd.DataFrame({
        '卖1价': [100, 100, 100],
        '卖2价': [100, 100, 100],
        '卖3价': [100, 100, 100],
        '卖4价': [100, 100, 100],
        '卖5价': [100, 100, 100],
        '卖6价': [100, 100, 100],
        '卖7价': [100, 100, 100],
        '卖8价': [100, 100, 100],
        '卖9价': [100, 100, 100],
        '卖10价': [100, 100, 100]
    })
    skew1 = df1.skew(axis=1)
    print(f"偏度: {skew1.values}")
    print(f"是否全为0: {np.allclose(skew1, 0)}\n")
    
    # 情况2: 对称分布
    print("情况2: 对称分布")
    df2 = pd.DataFrame({
        '卖1价': [95, 95, 95],
        '卖2价': [96, 96, 96],
        '卖3价': [97, 97, 97],
        '卖4价': [98, 98, 98],
        '卖5价': [99, 99, 99],
        '卖6价': [101, 101, 101],
        '卖7价': [102, 102, 102],
        '卖8价': [103, 103, 103],
        '卖9价': [104, 104, 104],
        '卖10价': [105, 105, 105]
    })
    skew2 = df2.skew(axis=1)
    print(f"偏度: {skew2.values}")
    print(f"是否接近0: {np.allclose(skew2, 0, atol=1e-10)}\n")
    
    # 情况3: 包含NaN值
    print("情况3: 包含NaN值")
    df3 = pd.DataFrame({
        '卖1价': [100, 100, 100],
        '卖2价': [101, 101, 101],
        '卖3价': [102, 102, 102],
        '卖4价': [103, 103, 103],
        '卖5价': [104, 104, 104],
        '卖6价': [np.nan, np.nan, np.nan],
        '卖7价': [np.nan, np.nan, np.nan],
        '卖8价': [np.nan, np.nan, np.nan],
        '卖9价': [np.nan, np.nan, np.nan],
        '卖10价': [np.nan, np.nan, np.nan]
    })
    skew3 = df3.skew(axis=1)
    print(f"偏度: {skew3.values}")
    print(f"是否包含NaN: {skew3.isna().any()}\n")
    
    # 情况4: 实际市场数据可能的情况
    print("情况4: 实际市场数据可能的情况")
    df4 = pd.DataFrame({
        '卖1价': [100, 100, 100],
        '卖2价': [100, 100, 100],
        '卖3价': [100, 100, 100],
        '卖4价': [100, 100, 100],
        '卖5价': [100, 100, 100],
        '卖6价': [100, 100, 100],
        '卖7价': [100, 100, 100],
        '卖8价': [100, 100, 100],
        '卖9价': [100, 100, 100],
        '卖10价': [100, 100, 100]
    })
    skew4 = df4.skew(axis=1)
    print(f"偏度: {skew4.values}")
    print(f"是否全为0: {np.allclose(skew4, 0)}")
    
    return df1, df2, df3, df4

if __name__ == "__main__":
    test_skew_zero_cases() 