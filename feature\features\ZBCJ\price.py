import pandas as pd
import numpy as np
from ...base_class import zb_base


class price_001(zb_base):
    """
    返回 3s标准 的mean_n_s s平均成交价格
    """

    def __init__(self, mean_n_s=1) -> None:
        self.mean_n_s = mean_n_s

    def raw_name(self):
        return "逐笔成交"

    def get_kwargs(self):
        return {
            'mean_n_s': self.mean_n_s,  # 均值计算的秒数区间
        }

    def name(self):
        """
        返回特征名称
        """
        return "价格"

    def version(self):
        return "001_20231023"

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """
        # 时间
        data["时间"] = pd.to_datetime(data["时间"])

        data["手"] = data["手"].astype(float).astype(int)

        data["价格"] = data["价格"].astype(float)

        data = data.set_index("时间")

        return data

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        # 切片交易时间内的数据
        data = pd.concat([data.between_time("09:30:00", "11:30:00"),
                          data.between_time("13:00:00", "15:00:00")])

        if len(data) == 0:
            return None

        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        # 计算平均价格
        data['amount'] = data['价格']*data['手']
        data['vol'] = data['手']

        target_data = data.resample(f"1S", closed="right", label="right").apply(
            {
                "amount": np.sum,
                "vol": np.sum,
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        # 计算 mean_n_s 的均价
        mean_n = self.get_kwargs()['mean_n_s']
        amount = target_data['amount'].values.copy()
        vol = target_data['vol'].values.copy()
        for i in range(1, mean_n):
            amount += target_data['amount'].shift(-i).values
            vol += target_data['vol'].shift(-i).values

        # 没有成交部分值处理
        vol = np.where(vol == 0, 1.0, vol)
        amount = np.where(amount == 0, np.nan, amount)
        target_data['mean_price'] = amount / vol
        target_data['mean_price'].fillna(method="ffill", inplace=True)

        # 标准到3s的时间戳
        # 添加空行，确保每个切片都有重采样数据
        target_data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        target_data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        target_data = target_data.resample(f"3S", closed="right", label="right").apply(
            {
                "mean_price": 'last',
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        return target_data['mean_price']

    def cal(self, data: pd.DataFrame):
        """
        计算数据
        """
        # 截取需要的列
        data = data.loc[:, ['时间', '序号', '价格', '手']]

        return super().cal(data)
