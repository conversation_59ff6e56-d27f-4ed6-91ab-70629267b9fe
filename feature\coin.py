import datetime
import pickle
import os
import pandas as pd
import numpy as np
from joblib import Parallel, delayed
from pymongo import MongoClient
from .base_class import mongo_base
from .tool import is_port_open, tz_beijing


class feature_geter_coin():
    def __init__(self, feature: mongo_base, target=None, data_path='') -> None:
        super().__init__()
        self.feature = feature
        self.target = target

        self.client = None
        ip = '***************'
        port = 27017
        if is_port_open(ip, port):
            self.client = MongoClient(ip, port)

        self.data_path = r'D:\code\featrue_data\binance_datas' if data_path == "" else data_path

        # 确保data文件夹
        if not os.path.exists(self.data_path):
            os.mkdir(self.data_path)

    def _get(self, code, _begin_timestamp, _end_timestamp):
        filename = f"{self.data_path}/{self.feature.name()}_{code}_{_begin_timestamp}_{_end_timestamp}.pkl"
        code_hour_feature = None
        if os.path.exists(filename):
            print(
                f'本地获取 {code} {_begin_timestamp} -> {_end_timestamp} 的数据')
            code_hour_feature = pickle.load(open(filename, 'rb'))
        elif not self.client is None:
            print(
                f'mongo 获取 {code} {_begin_timestamp} -> {_end_timestamp} 的数据')
            # 计算特征
            code_hour_feature = self.feature.get(
                code, self.client, _begin_timestamp, _end_timestamp)
            if None is code_hour_feature:
                print('数据为空，跳过。。。')
                return None

            # 计算标签
            if not None is self.target and 'target' not in list(code_hour_feature) and 'label' not in list(code_hour_feature):
                target_data = self.target.get(
                    code, self.client, _begin_timestamp, _end_timestamp)
                if None is target_data:
                    print('标签数据为空，跳过。。。')
                    return None

                cols = list(target_data)
                if 'target' in cols:
                    code_hour_feature['target'] = target_data['target']
                elif 'label' in cols:
                    code_hour_feature['target'] = target_data['label']
                else:
                    raise Exception(self.target.name() +
                                    '返回数据中没有 target/label 列，请检查')

            # 添加symbol
            code_hour_feature['code'] = code

            # 保存数据
            pickle.dump(code_hour_feature, open(filename, 'wb'))

        return code_hour_feature

    def get(self, codes, begin_date, begin_idx, end_idx):
        """
        begin_date : '2024-03-16'
        begin_idx/ end_idx: 用于切片的索引 包含 begin_idx 不包含 end_idx
        """
        if isinstance(codes, str):
            codes = [codes]

        # 获取 begin_date 0时的时间戳
        begin_timestamp = int(tz_beijing.localize(datetime.datetime.strptime(
            begin_date, '%Y-%m-%d')).timestamp())

        # # 创建一个Parallel对象并指定要使用的CPU核心数
        # parallel = Parallel(n_jobs=-1, prefer="threads")

        args = []
        for i in range(begin_idx, end_idx):
            # 获取范围时间戳
            _begin_timestamp = begin_timestamp + i*3600
            _end_timestamp = _begin_timestamp + 3600

            for code in codes:
                args.append((code, _begin_timestamp, _end_timestamp))

        # # 并行执行任务
        # results = parallel(
        #     delayed(self._get)(*arg) for arg in args)
        results = [self._get(*arg) for arg in args]

        # 合并单个标的的数据
        results = [i for i in results if not None is i]
        feature_data = pd.concat(results, ignore_index=True)

        # 排序
        # print(list(feature_data))
        feature_data = feature_data.sort_values(
            by=['code', 'id'], ignore_index=True)

        # 删除包含 target 的列名
        to_del_col = [i for i in list(feature_data) if 'target' in i]
        feature_data.drop(columns=to_del_col, inplace=True)

        return feature_data
