import pandas as pd
import numpy as np

import os
import torch
from tqdm import tqdm

import pickle
import zipfile
import shutil

from ZBCJ.zbcj import get_zbcj_factor
from feature_data.ZBWT.zbwt import get_zbwt_factor
from feature_data.time_point_data import get_bdgk_factor, get_sdpk_factor, get_yddl_factor
from tool import L2_Dataset_code_daily, save, read

BINS = [
    -np.inf,
    -0.003,
    -0.002,
    -0.001,
    0.001,
    0.002,
    0.003,
    np.inf,
]


def save_train_data(tau, rets_n):
    """
    tau: 使用的时间序列长度
    rets_n: 预测 n个数据后 的收益率
    """

    # 读取历史数据 日期文件夹 列表
    his_raw_path = "D:/通达信录制数据/his_data/raw/"
    raw_list = os.listdir(his_raw_path)

    # 读取训练数据 日期文件夹 列表
    train_folder = f"T{tau}_N{rets_n}"
    train_data_path = "D:/通达信录制数据/his_data/data/" + train_folder

    # 确保文件夹存在
    if not os.path.exists(train_data_path):
        os.makedirs(train_data_path)

    data_list = os.listdir(train_data_path)

    # 计算储存训练数据
    for date in raw_list:
        # 训练数据文件夹路径
        train_data_date_path = os.path.join(train_data_path, date)
        # 确保文件夹存在
        if not os.path.exists(train_data_date_path):
            os.makedirs(train_data_date_path)

        # 当前训练文件列表
        cur_train_datas = [i.split(".")[0]
                           for i in os.listdir(train_data_date_path)]

        # 日期文件夹路径
        path = os.path.join(his_raw_path, date)

        # 读取文件夹下所有股票zip文件
        files = os.listdir(path)

        # 未处理的数据
        files = [i for i in files if i.split(".")[0] not in cur_train_datas]

        loops = tqdm(files)
        for file in loops:
            loops.set_description(f"预处理 {date} {file.split('.')[0]}")

            # 训练数据文件名
            train_data_file_name = os.path.join(
                train_data_date_path, file.split(".")[0] + ".pickle"
            )

            # file -> 600000.zip
            temp_path = "D:/通达信录制数据/his_data/temp/"

            # 清空 temp 文件夹
            if os.path.exists(temp_path):
                shutil.rmtree(temp_path)

            # 股票zip文件路径
            file_path = os.path.join(path, file)

            fz = zipfile.ZipFile(file_path, "r")

            # 解压到temp文件夹
            for file in fz.namelist():
                # 不解压 raw 文件
                if "raw" in file:
                    continue

                fz.extract(file, path=temp_path)
                right_file = file.encode("cp437").decode("gbk")
                os.rename(
                    os.path.join(temp_path, file),
                    os.path.join(temp_path, right_file),
                )

            base_path = os.path.join(temp_path, list(fz.namelist())[0])

            # 计算因子
            # 如果返回 有None 则跳过
            zbcj_factor = get_zbcj_factor(base_path, date)
            if zbcj_factor is None:
                continue

            zbwt_factor = get_zbwt_factor(base_path, date)
            if zbwt_factor is None:
                continue

            bdgk_factor = get_bdgk_factor(base_path)
            if bdgk_factor is None:
                continue

            sdpk_factor = get_sdpk_factor(base_path)
            if sdpk_factor is None:
                continue

            yddl_factor = get_yddl_factor(base_path)
            if yddl_factor is None:
                continue

            # 合并所有dataframe
            all_factor = pd.merge(
                zbcj_factor, zbwt_factor, left_index=True, right_index=True
            )
            all_factor = pd.merge(
                all_factor, bdgk_factor, left_index=True, right_index=True
            )
            all_factor = pd.merge(
                all_factor, sdpk_factor, left_index=True, right_index=True
            )
            all_factor = pd.merge(
                all_factor, yddl_factor, left_index=True, right_index=True
            )

            # 标签 -> rets_n个数据后的对数收益率
            # all_factor["标签"] = np.log(
            #     all_factor["现价"].shift(-rets_n) / all_factor["现价"]
            # )

            # 标签 -> rets_n个数据后的收益率
            # all_factor["标签"] = all_factor["现价"].shift(-rets_n) / all_factor["现价"] - 1

            # 标签 -> rets_n个数据后的价格变动
            all_factor["标签"] = all_factor["现价"].shift(
                -rets_n) - all_factor["现价"]

            # 对标签进行 one-hot 编码
            all_factor["标签"] = pd.cut(
                all_factor["标签"],
                bins=BINS,
                labels=[i for i in range(len(BINS) - 1)],
            )

            all_factor = all_factor.dropna()

            # 对 label 进行 one-hot 编码
            laber = pd.get_dummies(all_factor["标签"]).astype("int8")
            all_factor = pd.concat([all_factor, laber], axis=1)

            # 删除标签列
            all_factor = all_factor.drop(["标签"], axis=1)

            # 生成训练数据
            train_data = L2_Dataset_code_daily(all_factor, tau, len(BINS) - 1)

            # 保存训练数据
            # torch.save(train_data, train_data_file_name)
            save(train_data, train_data_file_name)

            pass


if __name__ == "__main__":
    save_train_data(30, 5)
