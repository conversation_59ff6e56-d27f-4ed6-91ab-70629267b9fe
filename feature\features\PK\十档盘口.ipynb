{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f80b2091", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import shutil\n", "\n", "from config import get_realtime_data_path, get_his_data_path\n", "from time_point_data import _read_time_point_data"]}, {"cell_type": "code", "execution_count": 2, "id": "e605079b", "metadata": {}, "outputs": [], "source": ["# 设置项\n", "date = '20230901'"]}, {"cell_type": "code", "execution_count": 12, "id": "a8db3174", "metadata": {}, "outputs": [], "source": ["def format_unit(data):\n", "    # 格式处理\n", "        \n", "    # 单位处理\n", "    unit_cols_1 = ['总卖','总买']\n", "    data.loc[:, unit_cols_1] = data.loc[:, unit_cols_1].applymap(\n", "        lambda x: 10000 * (float(x.replace(\"万\", \"\")))\n", "        if \"万\" in str(x)\n", "        else 1e8 * (float(x.replace(\"亿\", \"\")))\n", "        if \"亿\" in str(x)\n", "        else float(x)\n", "    )\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": 9, "id": "df6e3ac1", "metadata": {}, "outputs": [], "source": ["file = os.path.join(get_his_data_path('513050', date), \"十档盘口.csv\")\n", "cols = [\n", "    '时间',\n", "    '卖10价',\n", "    '卖10量',\n", "    '卖9价',\n", "    '卖9量',\n", "    '卖8价',\n", "    '卖8量',\n", "    '卖7价',\n", "    '卖7量',\n", "    '卖6价',\n", "    '卖6量',\n", "    '卖5价',\n", "    '卖5量',\n", "    '卖4价',\n", "    '卖4量',\n", "    '卖3价',\n", "    '卖3量',\n", "    '卖2价',\n", "    '卖2量',\n", "    '卖1价',\n", "    '卖1量',\n", "    '买1价',\n", "    '买1量',\n", "    '买2价',\n", "    '买2量',\n", "    '买3价',\n", "    '买3量',\n", "    '买4价',\n", "    '买4量',\n", "    '买5价',\n", "    '买5量',\n", "    '买6价',\n", "    '买6量',\n", "    '买7价',\n", "    '买7量',\n", "    '买8价',\n", "    '买8量',\n", "    '买9价',\n", "    '买9量',\n", "    '买10价',\n", "    '买10量',\n", "    '卖均',\n", "    '总卖',\n", "    '买均',\n", "    '总买'\n", "]\n", "# data = pd.read_csv(file_his, encoding=\"gbk\", dtype=str)\n", "# data"]}, {"cell_type": "code", "execution_count": 10, "id": "4dd5637d", "metadata": {}, "outputs": [], "source": ["# # 读取数据\n", "# file = os.path.join(get_realtime_data_path(date), \"千档盘口.csv\")\n", "# # shutil.copy(file, '标的概况/标的概况.csv')\n", "# data = pd.read_csv(file, encoding=\"gbk\", dtype=str)\n", "\n", "# # 删除 时间 列中 \"时间未更新\" 的行\n", "# # 适配旧的历史数据\n", "# data = data.query('时间 != \"时间未更新\"').reset_index(drop=True)\n", "\n", "# # \n", "\n", "# data"]}, {"cell_type": "code", "execution_count": 13, "id": "01ecd295", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>卖10价</th>\n", "      <th>卖10量</th>\n", "      <th>卖9价</th>\n", "      <th>卖9量</th>\n", "      <th>卖8价</th>\n", "      <th>卖8量</th>\n", "      <th>卖7价</th>\n", "      <th>卖7量</th>\n", "      <th>卖6价</th>\n", "      <th>卖6量</th>\n", "      <th>...</th>\n", "      <th>买8价</th>\n", "      <th>买8量</th>\n", "      <th>买9价</th>\n", "      <th>买9量</th>\n", "      <th>买10价</th>\n", "      <th>买10量</th>\n", "      <th>卖均</th>\n", "      <th>总卖</th>\n", "      <th>买均</th>\n", "      <th>总买</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.073</td>\n", "      <td>11566.0</td>\n", "      <td>1.072</td>\n", "      <td>7735.0</td>\n", "      <td>1.071</td>\n", "      <td>8648.0</td>\n", "      <td>1.070</td>\n", "      <td>9394.0</td>\n", "      <td>1.069</td>\n", "      <td>20569.0</td>\n", "      <td>...</td>\n", "      <td>1.056</td>\n", "      <td>5633.0</td>\n", "      <td>1.055</td>\n", "      <td>3286.0</td>\n", "      <td>1.054</td>\n", "      <td>6362.0</td>\n", "      <td>1.084</td>\n", "      <td>248148.0</td>\n", "      <td>1.047</td>\n", "      <td>175645.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.073</td>\n", "      <td>11566.0</td>\n", "      <td>1.072</td>\n", "      <td>8352.0</td>\n", "      <td>1.071</td>\n", "      <td>8790.0</td>\n", "      <td>1.070</td>\n", "      <td>10305.0</td>\n", "      <td>1.069</td>\n", "      <td>21462.0</td>\n", "      <td>...</td>\n", "      <td>1.056</td>\n", "      <td>5800.0</td>\n", "      <td>1.055</td>\n", "      <td>3226.0</td>\n", "      <td>1.054</td>\n", "      <td>6336.0</td>\n", "      <td>1.083</td>\n", "      <td>278837.0</td>\n", "      <td>1.048</td>\n", "      <td>188742.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.073</td>\n", "      <td>13926.0</td>\n", "      <td>1.072</td>\n", "      <td>16591.0</td>\n", "      <td>1.071</td>\n", "      <td>11100.0</td>\n", "      <td>1.070</td>\n", "      <td>26197.0</td>\n", "      <td>1.069</td>\n", "      <td>25886.0</td>\n", "      <td>...</td>\n", "      <td>1.056</td>\n", "      <td>8449.0</td>\n", "      <td>1.055</td>\n", "      <td>5434.0</td>\n", "      <td>1.054</td>\n", "      <td>6326.0</td>\n", "      <td>1.080</td>\n", "      <td>350043.0</td>\n", "      <td>1.050</td>\n", "      <td>214650.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.074</td>\n", "      <td>14008.0</td>\n", "      <td>1.073</td>\n", "      <td>15126.0</td>\n", "      <td>1.072</td>\n", "      <td>16975.0</td>\n", "      <td>1.071</td>\n", "      <td>11100.0</td>\n", "      <td>1.070</td>\n", "      <td>26417.0</td>\n", "      <td>...</td>\n", "      <td>1.056</td>\n", "      <td>8590.0</td>\n", "      <td>1.055</td>\n", "      <td>5403.0</td>\n", "      <td>1.054</td>\n", "      <td>6326.0</td>\n", "      <td>1.081</td>\n", "      <td>353981.0</td>\n", "      <td>1.048</td>\n", "      <td>216559.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.073</td>\n", "      <td>15128.0</td>\n", "      <td>1.072</td>\n", "      <td>17080.0</td>\n", "      <td>1.071</td>\n", "      <td>11275.0</td>\n", "      <td>1.070</td>\n", "      <td>27850.0</td>\n", "      <td>1.069</td>\n", "      <td>29869.0</td>\n", "      <td>...</td>\n", "      <td>1.056</td>\n", "      <td>8580.0</td>\n", "      <td>1.055</td>\n", "      <td>5441.0</td>\n", "      <td>1.054</td>\n", "      <td>8436.0</td>\n", "      <td>1.080</td>\n", "      <td>373729.0</td>\n", "      <td>1.049</td>\n", "      <td>225667.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.076</td>\n", "      <td>5787.0</td>\n", "      <td>1.075</td>\n", "      <td>17734.0</td>\n", "      <td>1.074</td>\n", "      <td>19756.0</td>\n", "      <td>1.073</td>\n", "      <td>19140.0</td>\n", "      <td>1.072</td>\n", "      <td>20908.0</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>2888.0</td>\n", "      <td>1.058</td>\n", "      <td>5114.0</td>\n", "      <td>1.057</td>\n", "      <td>7846.0</td>\n", "      <td>1.083</td>\n", "      <td>519710.0</td>\n", "      <td>1.053</td>\n", "      <td>281874.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.076</td>\n", "      <td>5787.0</td>\n", "      <td>1.075</td>\n", "      <td>17734.0</td>\n", "      <td>1.074</td>\n", "      <td>19756.0</td>\n", "      <td>1.073</td>\n", "      <td>19140.0</td>\n", "      <td>1.072</td>\n", "      <td>20908.0</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>2888.0</td>\n", "      <td>1.058</td>\n", "      <td>5114.0</td>\n", "      <td>1.057</td>\n", "      <td>7846.0</td>\n", "      <td>1.083</td>\n", "      <td>519603.0</td>\n", "      <td>1.053</td>\n", "      <td>278682.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.076</td>\n", "      <td>5787.0</td>\n", "      <td>1.075</td>\n", "      <td>17734.0</td>\n", "      <td>1.074</td>\n", "      <td>19756.0</td>\n", "      <td>1.073</td>\n", "      <td>19140.0</td>\n", "      <td>1.072</td>\n", "      <td>20908.0</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>2888.0</td>\n", "      <td>1.058</td>\n", "      <td>5114.0</td>\n", "      <td>1.057</td>\n", "      <td>7846.0</td>\n", "      <td>1.083</td>\n", "      <td>519140.0</td>\n", "      <td>1.053</td>\n", "      <td>276410.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.076</td>\n", "      <td>5787.0</td>\n", "      <td>1.075</td>\n", "      <td>17734.0</td>\n", "      <td>1.074</td>\n", "      <td>19756.0</td>\n", "      <td>1.073</td>\n", "      <td>18140.0</td>\n", "      <td>1.072</td>\n", "      <td>20908.0</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>2887.0</td>\n", "      <td>1.058</td>\n", "      <td>5114.0</td>\n", "      <td>1.057</td>\n", "      <td>7846.0</td>\n", "      <td>1.083</td>\n", "      <td>517154.0</td>\n", "      <td>1.053</td>\n", "      <td>273225.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.076</td>\n", "      <td>5787.0</td>\n", "      <td>1.075</td>\n", "      <td>17734.0</td>\n", "      <td>1.074</td>\n", "      <td>19756.0</td>\n", "      <td>1.073</td>\n", "      <td>18140.0</td>\n", "      <td>1.072</td>\n", "      <td>20908.0</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>2887.0</td>\n", "      <td>1.058</td>\n", "      <td>5114.0</td>\n", "      <td>1.057</td>\n", "      <td>7846.0</td>\n", "      <td>1.083</td>\n", "      <td>517154.0</td>\n", "      <td>1.053</td>\n", "      <td>273225.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 44 columns</p>\n", "</div>"], "text/plain": ["                      卖10价     卖10量    卖9价      卖9量    卖8价      卖8量    卖7价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.073  11566.0  1.072   7735.0  1.071   8648.0  1.070   \n", "2023-09-01 09:30:06  1.073  11566.0  1.072   8352.0  1.071   8790.0  1.070   \n", "2023-09-01 09:30:09  1.073  13926.0  1.072  16591.0  1.071  11100.0  1.070   \n", "2023-09-01 09:30:12  1.074  14008.0  1.073  15126.0  1.072  16975.0  1.071   \n", "2023-09-01 09:30:15  1.073  15128.0  1.072  17080.0  1.071  11275.0  1.070   \n", "...                    ...      ...    ...      ...    ...      ...    ...   \n", "2023-09-01 14:59:48  1.076   5787.0  1.075  17734.0  1.074  19756.0  1.073   \n", "2023-09-01 14:59:51  1.076   5787.0  1.075  17734.0  1.074  19756.0  1.073   \n", "2023-09-01 14:59:54  1.076   5787.0  1.075  17734.0  1.074  19756.0  1.073   \n", "2023-09-01 14:59:57  1.076   5787.0  1.075  17734.0  1.074  19756.0  1.073   \n", "2023-09-01 15:00:00  1.076   5787.0  1.075  17734.0  1.074  19756.0  1.073   \n", "\n", "                         卖7量    卖6价      卖6量  ...    买8价     买8量    买9价  \\\n", "时间                                            ...                         \n", "2023-09-01 09:30:03   9394.0  1.069  20569.0  ...  1.056  5633.0  1.055   \n", "2023-09-01 09:30:06  10305.0  1.069  21462.0  ...  1.056  5800.0  1.055   \n", "2023-09-01 09:30:09  26197.0  1.069  25886.0  ...  1.056  8449.0  1.055   \n", "2023-09-01 09:30:12  11100.0  1.070  26417.0  ...  1.056  8590.0  1.055   \n", "2023-09-01 09:30:15  27850.0  1.069  29869.0  ...  1.056  8580.0  1.055   \n", "...                      ...    ...      ...  ...    ...     ...    ...   \n", "2023-09-01 14:59:48  19140.0  1.072  20908.0  ...  1.059  2888.0  1.058   \n", "2023-09-01 14:59:51  19140.0  1.072  20908.0  ...  1.059  2888.0  1.058   \n", "2023-09-01 14:59:54  19140.0  1.072  20908.0  ...  1.059  2888.0  1.058   \n", "2023-09-01 14:59:57  18140.0  1.072  20908.0  ...  1.059  2887.0  1.058   \n", "2023-09-01 15:00:00  18140.0  1.072  20908.0  ...  1.059  2887.0  1.058   \n", "\n", "                        买9量   买10价    买10量     卖均        总卖     买均        总买  \n", "时间                                                                            \n", "2023-09-01 09:30:03  3286.0  1.054  6362.0  1.084  248148.0  1.047  175645.0  \n", "2023-09-01 09:30:06  3226.0  1.054  6336.0  1.083  278837.0  1.048  188742.0  \n", "2023-09-01 09:30:09  5434.0  1.054  6326.0  1.080  350043.0  1.050  214650.0  \n", "2023-09-01 09:30:12  5403.0  1.054  6326.0  1.081  353981.0  1.048  216559.0  \n", "2023-09-01 09:30:15  5441.0  1.054  8436.0  1.080  373729.0  1.049  225667.0  \n", "...                     ...    ...     ...    ...       ...    ...       ...  \n", "2023-09-01 14:59:48  5114.0  1.057  7846.0  1.083  519710.0  1.053  281874.0  \n", "2023-09-01 14:59:51  5114.0  1.057  7846.0  1.083  519603.0  1.053  278682.0  \n", "2023-09-01 14:59:54  5114.0  1.057  7846.0  1.083  519140.0  1.053  276410.0  \n", "2023-09-01 14:59:57  5114.0  1.057  7846.0  1.083  517154.0  1.053  273225.0  \n", "2023-09-01 15:00:00  5114.0  1.057  7846.0  1.083  517154.0  1.053  273225.0  \n", "\n", "[4800 rows x 44 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data = _read_time_point_data(file, cols, format_unit)\n", "data"]}, {"cell_type": "code", "execution_count": 41, "id": "1765ae84", "metadata": {}, "outputs": [], "source": ["# cond = (data['买10价'] - data['买10价'].shift()).apply(lambda x:1 if x>0 else -1 if x<0 else np.nan)\n", "# cond.head(35)"]}, {"cell_type": "code", "execution_count": 42, "id": "51162f85", "metadata": {}, "outputs": [], "source": ["# v1 = data['买10量'] * cond\n", "# v1.head(35)"]}, {"cell_type": "code", "execution_count": 43, "id": "60521492", "metadata": {}, "outputs": [], "source": ["# v2 = data['买10量'] - data['买10量'].shift()\n", "# v2.head(35)"]}, {"cell_type": "code", "execution_count": 44, "id": "e28d6e1a", "metadata": {}, "outputs": [], "source": ["# v = v1.fillna(v2)\n", "# v.head(35)"]}, {"cell_type": "code", "execution_count": 45, "id": "04b1e5c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03       NaN\n", "2023-09-01 09:30:06     -26.0\n", "2023-09-01 09:30:09     -10.0\n", "2023-09-01 09:30:12       0.0\n", "2023-09-01 09:30:15    2110.0\n", "2023-09-01 09:30:18     487.0\n", "2023-09-01 09:30:21       0.0\n", "2023-09-01 09:30:24       0.0\n", "2023-09-01 09:30:27       0.0\n", "2023-09-01 09:30:30       0.0\n", "2023-09-01 09:30:33       0.0\n", "2023-09-01 09:30:36   -2110.0\n", "2023-09-01 09:30:39     -37.0\n", "2023-09-01 09:30:42       0.0\n", "2023-09-01 09:30:45       0.0\n", "2023-09-01 09:30:48       0.0\n", "2023-09-01 09:30:51   -1372.0\n", "2023-09-01 09:30:54       0.0\n", "2023-09-01 09:30:57      10.0\n", "2023-09-01 09:31:00    8000.0\n", "2023-09-01 09:31:03   -8000.0\n", "2023-09-01 09:31:06       0.0\n", "2023-09-01 09:31:09       0.0\n", "2023-09-01 09:31:12       0.0\n", "2023-09-01 09:31:15       0.0\n", "2023-09-01 09:31:18       0.0\n", "2023-09-01 09:31:21     -10.0\n", "2023-09-01 09:31:24       0.0\n", "2023-09-01 09:31:27       0.0\n", "2023-09-01 09:31:30       0.0\n", "2023-09-01 09:31:33       0.0\n", "2023-09-01 09:31:36       0.0\n", "2023-09-01 09:31:39       0.0\n", "2023-09-01 09:31:42       0.0\n", "2023-09-01 09:31:45       0.0\n", "dtype: float64"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["cond = (data['买10价'] - data['买10价'].shift()).apply(lambda x:1 if x>0 else -1 if x<0 else np.nan)\n", "cond.head(35)\n", "v1 = data['买10量'] * cond\n", "v1.head(35)\n", "v2 = data['买10量'] - data['买10量'].shift()\n", "v2.head(35)\n", "v = v1.fillna(v2)\n", "v.head(35)"]}, {"cell_type": "code", "execution_count": 46, "id": "78c29da3", "metadata": {}, "outputs": [], "source": ["# cond = (data['卖10价'] - data['卖10价'].shift()).apply(lambda x:1 if x>0 else -1 if x<0 else np.nan)\n", "# cond.head(35)"]}, {"cell_type": "code", "execution_count": 47, "id": "244ad805", "metadata": {}, "outputs": [], "source": ["# v1 = data['卖10量'] * cond * -1\n", "# v1.head(35)"]}, {"cell_type": "code", "execution_count": 48, "id": "37c418d5", "metadata": {}, "outputs": [], "source": ["# v2 = data['卖10量'] - data['卖10量'].shift()\n", "# v2.head(35)"]}, {"cell_type": "code", "execution_count": 49, "id": "c8b29ace", "metadata": {}, "outputs": [], "source": ["# v = v1.fillna(v2)\n", "# v.head(35)"]}, {"cell_type": "code", "execution_count": 50, "id": "7f109b3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03        NaN\n", "2023-09-01 09:30:06        0.0\n", "2023-09-01 09:30:09     2360.0\n", "2023-09-01 09:30:12   -14008.0\n", "2023-09-01 09:30:15    15128.0\n", "2023-09-01 09:30:18     2228.0\n", "2023-09-01 09:30:21      410.0\n", "2023-09-01 09:30:24       24.0\n", "2023-09-01 09:30:27        0.0\n", "2023-09-01 09:30:30        0.0\n", "2023-09-01 09:30:33        0.0\n", "2023-09-01 09:30:36    -2100.0\n", "2023-09-01 09:30:39        0.0\n", "2023-09-01 09:30:42        0.0\n", "2023-09-01 09:30:45        0.0\n", "2023-09-01 09:30:48        0.0\n", "2023-09-01 09:30:51    19087.0\n", "2023-09-01 09:30:54      -60.0\n", "2023-09-01 09:30:57        0.0\n", "2023-09-01 09:31:00        0.0\n", "2023-09-01 09:31:03    -2110.0\n", "2023-09-01 09:31:06        0.0\n", "2023-09-01 09:31:09        0.0\n", "2023-09-01 09:31:12      150.0\n", "2023-09-01 09:31:15        0.0\n", "2023-09-01 09:31:18        0.0\n", "2023-09-01 09:31:21      100.0\n", "2023-09-01 09:31:24        0.0\n", "2023-09-01 09:31:27        0.0\n", "2023-09-01 09:31:30        0.0\n", "2023-09-01 09:31:33        0.0\n", "2023-09-01 09:31:36        0.0\n", "2023-09-01 09:31:39        0.0\n", "2023-09-01 09:31:42        0.0\n", "2023-09-01 09:31:45        0.0\n", "dtype: float64"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["cond = (data['卖10价'] - data['卖10价'].shift()).apply(lambda x:1 if x>0 else -1 if x<0 else np.nan)\n", "cond.head(35)\n", "v1 = data['卖10量'] * cond * -1\n", "v1.head(35)\n", "v2 = data['卖10量'] - data['卖10量'].shift()\n", "v2.head(35)\n", "v = v1.fillna(v2)\n", "v.head(35)"]}, {"cell_type": "code", "execution_count": null, "id": "cbf3b21c", "metadata": {}, "outputs": [], "source": ["cond = (data['卖10价'] - data['卖10价'].shift()).apply(lambda x:1 if x>0 else -1 if x<0 else np.nan)\n", "v1 = data['卖10量'] * cond * -1\n", "v2 = data['卖10量'] - data['卖10量'].shift()\n", "v = v1.fillna(v2)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}