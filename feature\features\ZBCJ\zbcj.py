import pickle
import pandas as pd
import numpy as np
from datetime import timedelta
from ...tool import top_to_cols
from ...base_class import zb_feature, zb_base

# 成交类型
# 买 卖 集合竞价
DEAL_TYPES = ["B", "S", "JHJJ"]
B, S, JHJJ = range(3)


class zbcj_001(zb_feature):
    """
    1 + 68

    '时间' +
    ['ZBCJ_区间成交', 'ZBCJ_区间成买', 'ZBCJ_区间成卖', 'ZBCJ_区间手数', 'ZBCJ_区间买手', 'ZBCJ_区间卖手', 'ZBCJ_区间金额', 'ZBCJ_区间买额', 'ZBCJ_区间卖额', 'ZBCJ_累计成交', 'ZBCJ_累计成买', 'ZBCJ_累计成卖', 'ZBCJ_累计手数', 'ZBCJ_累计买手', 'ZBCJ_累计卖手', 'ZBCJ_累计金额', 'ZBCJ_累计买额', 'ZBCJ_累计卖额', 'ZBCJ_区间VWAP', 'ZBCJ_累计VWAP', 'ZBCJ_区间VWAP买', 'ZBCJ_累计VWAP买', 'ZBCJ_区间VWAP卖', 'ZBCJ_累计VWAP卖', 'ZBCJ_区间买卖比', 'ZBCJ_区间买卖手比', 'ZBCJ_累计买卖比', 'ZBCJ_累计买卖手比', 'ZBCJ_委买手1价', 'ZBCJ_委买手1手', 'ZBCJ_委买手2价', 'ZBCJ_委买手2手', 'ZBCJ_委买手3价', 'ZBCJ_委买手3手', 'ZBCJ_委买手4价', 'ZBCJ_委买手4手', 'ZBCJ_委买手5价', 'ZBCJ_委买手5手', 'ZBCJ_委买手6价', 'ZBCJ_委买手6手', 'ZBCJ_委买手7价', 'ZBCJ_委买手7手', 'ZBCJ_委买手8价', 'ZBCJ_委买手8手', 'ZBCJ_委买手9价', 'ZBCJ_委买手9手', 'ZBCJ_委买手10价', 'ZBCJ_委买手10手', 'ZBCJ_委卖手1价', 'ZBCJ_委卖手1手', 'ZBCJ_委卖手2价', 'ZBCJ_委卖手2手', 'ZBCJ_委卖手3价', 'ZBCJ_委卖手3手', 'ZBCJ_委卖手4价', 'ZBCJ_委卖手4手', 'ZBCJ_委卖手5价', 'ZBCJ_委卖手5手', 'ZBCJ_委卖手6价', 'ZBCJ_委卖手6手', 'ZBCJ_委卖手7价', 'ZBCJ_委卖手7手', 'ZBCJ_委卖手8价', 'ZBCJ_委卖手8手', 'ZBCJ_委卖手9价', 'ZBCJ_委卖手9手', 'ZBCJ_委卖手10价', 'ZBCJ_委卖手10手']
    """

    def __init__(self, kwargs: dict = {"top_n": 5}):
        super().__init__(kwargs)

    def top_func(self, end_dt: str, data: pd.DataFrame, top_n: int):
        # 时间筛选
        begin_dt = end_dt-timedelta(seconds=3)
        time_data = data.loc[(data.index >= begin_dt) &
                             (data.index < end_dt), :]
        if len(time_data) == 0:
            return pd.DataFrame()

        # 291 ms
        # 买卖筛选
        buy_trade = time_data.loc[time_data['类型'] == B, :]
        sell_trade = time_data.loc[time_data['类型'] == S, :]

        # 排序
        _buy_type_name = "买"
        _sell_type_name = "卖"
        sort_by_hand = ["手", "价格"]
        sort_by_price = ["价格", "手"]

        buy_trade_vol = buy_trade.sort_values(
            sort_by_hand, ascending=[False, False]).iloc[:top_n, 1:3].reset_index(drop=True)
        sell_trade_vol = sell_trade.sort_values(
            sort_by_hand, ascending=[False, True]).iloc[:top_n, 1:3].reset_index(drop=True)
        buy_trade_price = buy_trade.sort_values(
            sort_by_price, ascending=[False, False]).iloc[:top_n, 1:3].reset_index(drop=True)
        sell_trade_price = sell_trade.sort_values(
            sort_by_price, ascending=[True, False]).iloc[:top_n, 1:3].reset_index(drop=True)

        # return pd.DataFrame()
        # 合并返回
        return pd.concat([
            top_to_cols(buy_trade_vol, _buy_type_name,
                        sort_by_hand, end_dt, top_n),
            top_to_cols(sell_trade_vol, _sell_type_name,
                        sort_by_hand, end_dt, top_n),
            top_to_cols(buy_trade_price, _buy_type_name,
                        sort_by_price, end_dt, top_n),
            top_to_cols(sell_trade_price, _sell_type_name,
                        sort_by_price, end_dt, top_n),
        ], axis=1)

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据
        """
        # 时间
        data["时间"] = pd.to_datetime(data["时间"])

        data["类型"] = data["类型"].fillna("JHJJ")
        data["类型"] = data["类型"].apply(lambda x: DEAL_TYPES.index(x))

        data["手"] = data["手"].astype(float).astype(int)

        data["价格"] = data["价格"].astype(float)

        data = data.set_index("时间")

        return data

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        # 切片交易时间内的数据
        data = pd.concat([data.between_time("09:30:00", "11:30:00"),
                          data.between_time("13:00:00", "14:55:00")])

        if len(data) == 0:
            return None

        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        data.loc[pd.to_datetime(date_str + "14:55:01")] = np.nan

        # 订单数量
        data["区间成交"] = 1
        data["区间成买"] = data["类型"].apply(lambda x: 1 if x == B else 0)
        data["区间成卖"] = data["类型"].apply(lambda x: 1 if x == S else 0)

        # 手数
        data["区间手数"] = data["手"]
        data["区间买手"] = data["手"] * data["区间成买"]
        data["区间卖手"] = data["手"] * data["区间成卖"]

        # 额
        data["区间金额"] = data["手"] * data["价格"]
        data["区间买额"] = data["区间买手"] * data["价格"]
        data["区间卖额"] = data["区间卖手"] * data["价格"]

        std_data = data.resample("3s", label="right").apply(
            {
                "区间成交": 'sum',
                "区间成买": 'sum',
                "区间成卖": 'sum',
                "区间手数": 'sum',
                "区间买手": 'sum',
                "区间卖手": 'sum',
                "区间金额": 'sum',
                "区间买额": 'sum',
                "区间卖额": 'sum',
            }
        )

        # 删除最后一行 第一行
        std_data = std_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = std_data.between_time("09:30:01", "11:30:00")
        pm = std_data.between_time("13:00:01", "14:55:00")
        std_data = std_data.loc[am.index.union(pm.index), :]

        # 增加累计数据
        std_data["累计成交"] = std_data["区间成交"].cumsum()
        std_data["累计成买"] = std_data["区间成买"].cumsum()
        std_data["累计成卖"] = std_data["区间成卖"].cumsum()
        std_data["累计手数"] = std_data["区间手数"].cumsum()
        std_data["累计买手"] = std_data["区间买手"].cumsum()
        std_data["累计卖手"] = std_data["区间卖手"].cumsum()
        std_data["累计金额"] = std_data["区间金额"].cumsum()
        std_data["累计买额"] = std_data["区间买额"].cumsum()
        std_data["累计卖额"] = std_data["区间卖额"].cumsum()

        # ['区间VWAP', '累计VWAP', '区间VWAP买', '累计VWAP买', '区间VWAP卖', '累计VWAP卖', '区间买卖比', '区间买卖手比', '累计买卖比', '累计买卖手比']
        std_data["区间VWAP"] = std_data["区间金额"] / std_data["区间手数"]
        std_data["累计VWAP"] = std_data["累计金额"] / std_data["累计手数"]
        std_data["区间VWAP买"] = std_data["区间买额"] / std_data["区间买手"]
        std_data["累计VWAP买"] = std_data["累计买额"] / std_data["累计买手"]
        std_data["区间VWAP卖"] = std_data["区间卖额"] / std_data["区间卖手"]
        std_data["累计VWAP卖"] = std_data["累计卖额"] / std_data["累计卖手"]

        std_data["区间买卖比"] = std_data["区间成买"] / std_data["区间成卖"]
        std_data["区间买卖手比"] = std_data["区间买手"] / std_data["区间卖手"]
        std_data["累计买卖比"] = std_data["累计成买"] / std_data["累计成卖"]
        std_data["累计买卖手比"] = std_data["累计买手"] / std_data["累计卖手"]

        # 不允许存在nan的列
        nan_cols = ['区间VWAP', '累计VWAP', '区间VWAP买', '累计VWAP买', '区间VWAP卖', '累计VWAP卖', '区间买卖比', '区间买卖手比', '累计买卖比', '累计买卖手比']
        no_nan_cols = [i for i in list(std_data) if i not in nan_cols]
        assert not std_data.loc[:, no_nan_cols].isnull().values.any() and not np.isinf(std_data.loc[:, no_nan_cols]).values.any()

        # 增加top统计数据
        std_data = self.add_top_n_data(std_data, data)

        # 添加前缀
        std_data.columns = [f'ZBCJ_{i}' for i in list(std_data)]

        return std_data

    def raw_name(self):
        """
        返回特征原始文件名称
        """
        return "逐笔成交"

    def version(self):
        """
        返回特征版本号
        001_20230911
        """
        return "001_20230911"

    def name(self):
        """
        返回特征名称
        """
        return "逐笔成交"
