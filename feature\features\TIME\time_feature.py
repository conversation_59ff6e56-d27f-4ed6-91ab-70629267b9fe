import pandas as pd
import numpy as np

from ...base_class import time_feature


class tf_001(time_feature):
    """
    1+ 2
    '时间' + ['30min_idx', 'weekday_sin']
    """

    def add_time_feature(self, time_point: pd.DataFrame):
        date = str(time_point.index[0].date())

        # 所属的30min序号，第几个30min
        time_point["30min_idx"] = (time_point.index - pd.Timedelta(1, 's') -
                                   pd.to_datetime(date + " 09:30:00")).total_seconds() // (60 * 30) + 1
        time_point["30min_idx"] = time_point["30min_idx"].apply(
            lambda x: x if x < 5 else x-3)  # 中午休盘时间扣除

        # 时点所属的星期
        time_point['weekday_sin'] = np.sin(
            (time_point.index.weekday + 1)*(2.*np.pi/5))

        return time_point

    def get_kwargs(self):
        return {}

    def version(self):
        return "001_20230911"

    def name(self):
        """
        返回特征名称
        """
        return "时间"
