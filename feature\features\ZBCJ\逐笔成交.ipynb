{"cells": [{"cell_type": "code", "execution_count": 1, "id": "697d4807", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import os\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "859c1a9f", "metadata": {}, "outputs": [], "source": ["# 成交类型\n", "# 买 卖 集合竞价\n", "DEAL_TYPES = [\"B\", \"S\", \"JHJJ\"]\n", "B, S, JHJJ = range(3)\n", "\n", "# 基础路径\n", "root = r\"D:\\通达信录制数据\\realtime_data\\data\""]}, {"cell_type": "code", "execution_count": 3, "id": "0f8a12b2", "metadata": {}, "outputs": [], "source": ["# 设置项\n", "date = '20230901'"]}, {"cell_type": "markdown", "id": "7e9498e3", "metadata": {}, "source": ["# 获取数据"]}, {"cell_type": "code", "execution_count": 4, "id": "0166d929", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>09:25:00</td>\n", "      <td>57</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>09:25:00</td>\n", "      <td>58</td>\n", "      <td>1.064</td>\n", "      <td>20</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>09:25:00</td>\n", "      <td>59</td>\n", "      <td>1.064</td>\n", "      <td>12</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>09:25:00</td>\n", "      <td>60</td>\n", "      <td>1.064</td>\n", "      <td>150</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>09:25:00</td>\n", "      <td>61</td>\n", "      <td>1.064</td>\n", "      <td>205</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13019</th>\n", "      <td>14:59:59</td>\n", "      <td>2</td>\n", "      <td>1.066</td>\n", "      <td>200</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13020</th>\n", "      <td>14:59:59</td>\n", "      <td>3</td>\n", "      <td>1.066</td>\n", "      <td>5</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13021</th>\n", "      <td>14:59:59</td>\n", "      <td>4</td>\n", "      <td>1.066</td>\n", "      <td>1349</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13022</th>\n", "      <td>14:59:59</td>\n", "      <td>5</td>\n", "      <td>1.067</td>\n", "      <td>50</td>\n", "      <td>B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13023</th>\n", "      <td>14:59:59</td>\n", "      <td>6</td>\n", "      <td>1.066</td>\n", "      <td>100</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13024 rows × 5 columns</p>\n", "</div>"], "text/plain": ["             时间  序号     价格     手   类型\n", "0      09:25:00  57  1.064    10  NaN\n", "1      09:25:00  58  1.064    20  NaN\n", "2      09:25:00  59  1.064    12  NaN\n", "3      09:25:00  60  1.064   150  NaN\n", "4      09:25:00  61  1.064   205  NaN\n", "...         ...  ..    ...   ...  ...\n", "13019  14:59:59   2  1.066   200    S\n", "13020  14:59:59   3  1.066     5    S\n", "13021  14:59:59   4  1.066  1349    S\n", "13022  14:59:59   5  1.067    50    B\n", "13023  14:59:59   6  1.066   100    S\n", "\n", "[13024 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取数据\n", "file = os.path.join(os.path.join(root, date), \"逐笔成交.csv\")\n", "data = pd.read_csv(file, encoding=\"gbk\")\n", "data"]}, {"cell_type": "code", "execution_count": 5, "id": "a2a0ab62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>57</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>58</td>\n", "      <td>1.064</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>59</td>\n", "      <td>1.064</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>60</td>\n", "      <td>1.064</td>\n", "      <td>150</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>61</td>\n", "      <td>1.064</td>\n", "      <td>205</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>2</td>\n", "      <td>1.066</td>\n", "      <td>200</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>3</td>\n", "      <td>1.066</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>4</td>\n", "      <td>1.066</td>\n", "      <td>1349</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>5</td>\n", "      <td>1.067</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>6</td>\n", "      <td>1.066</td>\n", "      <td>100</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13024 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                     序号     价格     手  类型\n", "时间                                      \n", "2023-09-01 09:25:00  57  1.064    10   2\n", "2023-09-01 09:25:00  58  1.064    20   2\n", "2023-09-01 09:25:00  59  1.064    12   2\n", "2023-09-01 09:25:00  60  1.064   150   2\n", "2023-09-01 09:25:00  61  1.064   205   2\n", "...                  ..    ...   ...  ..\n", "2023-09-01 14:59:59   2  1.066   200   1\n", "2023-09-01 14:59:59   3  1.066     5   1\n", "2023-09-01 14:59:59   4  1.066  1349   1\n", "2023-09-01 14:59:59   5  1.067    50   0\n", "2023-09-01 14:59:59   6  1.066   100   1\n", "\n", "[13024 rows x 4 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 格式化\n", "# 时间\n", "_date = date + \" \"\n", "data[\"时间\"] = data[\"时间\"].apply(\n", "    lambda x: \"09:30:01\" if x == \"09:30:00\" else x\n", ")  # 09:30:00 -> 09:30:01\n", "data[\"时间\"] = data[\"时间\"].apply(\n", "    lambda x: \"13:00:01\" if x == \"13:00:00\" else x\n", ")  # 13:00:00 -> 13:00:01\n", "data[\"时间\"] = data[\"时间\"].apply(lambda x: _date + x)\n", "data[\"时间\"] = pd.to_datetime(data[\"时间\"])\n", "\n", "data[\"类型\"] = data[\"类型\"].fillna(\"JHJJ\")\n", "data[\"类型\"] = data[\"类型\"].apply(lambda x: DEAL_TYPES.index(x))\n", "\n", "data[\"手\"] = data[\"手\"].astype(float).astype(int)\n", "\n", "data = data.set_index(\"时间\")\n", "data"]}, {"cell_type": "code", "execution_count": 6, "id": "36bde2f6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>1</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>2</td>\n", "      <td>1.064</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>3</td>\n", "      <td>1.064</td>\n", "      <td>20</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>4</td>\n", "      <td>1.064</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>5</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>2</td>\n", "      <td>1.066</td>\n", "      <td>200</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>3</td>\n", "      <td>1.066</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>4</td>\n", "      <td>1.066</td>\n", "      <td>1349</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>5</td>\n", "      <td>1.067</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>6</td>\n", "      <td>1.066</td>\n", "      <td>100</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>354352 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                     序号     价格     手  类型\n", "时间                                      \n", "2023-09-01 09:30:01   1  1.064    10   1\n", "2023-09-01 09:30:01   2  1.064     5   1\n", "2023-09-01 09:30:01   3  1.064    20   1\n", "2023-09-01 09:30:01   4  1.064   150   1\n", "2023-09-01 09:30:01   5  1.064    10   1\n", "...                  ..    ...   ...  ..\n", "2023-09-01 14:59:59   2  1.066   200   1\n", "2023-09-01 14:59:59   3  1.066     5   1\n", "2023-09-01 14:59:59   4  1.066  1349   1\n", "2023-09-01 14:59:59   5  1.067    50   0\n", "2023-09-01 14:59:59   6  1.066   100   1\n", "\n", "[354352 rows x 4 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 切片交易时间内的数据\n", "data = data.loc[data.between_time(\"09:30:00\", \"15:00:00\").index, :]\n", "data"]}, {"cell_type": "code", "execution_count": 7, "id": "fc1dd226", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>1</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>2</td>\n", "      <td>1.064</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>3</td>\n", "      <td>1.064</td>\n", "      <td>20</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>4</td>\n", "      <td>1.064</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>5</td>\n", "      <td>1.064</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>2</td>\n", "      <td>1.066</td>\n", "      <td>200</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>3</td>\n", "      <td>1.066</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>4</td>\n", "      <td>1.066</td>\n", "      <td>1349</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>5</td>\n", "      <td>1.067</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>6</td>\n", "      <td>1.066</td>\n", "      <td>100</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>354352 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                     序号     价格     手  类型\n", "时间                                      \n", "2023-09-01 09:30:01   1  1.064    10   1\n", "2023-09-01 09:30:01   2  1.064     5   1\n", "2023-09-01 09:30:01   3  1.064    20   1\n", "2023-09-01 09:30:01   4  1.064   150   1\n", "2023-09-01 09:30:01   5  1.064    10   1\n", "...                  ..    ...   ...  ..\n", "2023-09-01 14:59:59   2  1.066   200   1\n", "2023-09-01 14:59:59   3  1.066     5   1\n", "2023-09-01 14:59:59   4  1.066  1349   1\n", "2023-09-01 14:59:59   5  1.067    50   0\n", "2023-09-01 14:59:59   6  1.066   100   1\n", "\n", "[354352 rows x 4 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "markdown", "id": "15193446", "metadata": {}, "source": ["# 数据特征"]}, {"cell_type": "code", "execution_count": 8, "id": "6f91ac94", "metadata": {}, "outputs": [{"data": {"text/plain": ["序号      int64\n", "价格    float64\n", "手       int32\n", "类型      int64\n", "dtype: object"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": 9, "id": "58f33f90", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>354352.000000</td>\n", "      <td>354352.000000</td>\n", "      <td>354352.000000</td>\n", "      <td>354352.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>75.506906</td>\n", "      <td>1.064601</td>\n", "      <td>320.874754</td>\n", "      <td>0.171471</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>73.713793</td>\n", "      <td>0.001957</td>\n", "      <td>831.973935</td>\n", "      <td>0.376920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.060000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>14.000000</td>\n", "      <td>1.063000</td>\n", "      <td>20.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>47.000000</td>\n", "      <td>1.065000</td>\n", "      <td>89.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>122.000000</td>\n", "      <td>1.066000</td>\n", "      <td>220.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>285.000000</td>\n", "      <td>1.069000</td>\n", "      <td>9990.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  序号             价格              手             类型\n", "count  354352.000000  354352.000000  354352.000000  354352.000000\n", "mean       75.506906       1.064601     320.874754       0.171471\n", "std        73.713793       0.001957     831.973935       0.376920\n", "min         1.000000       1.060000       0.000000       0.000000\n", "25%        14.000000       1.063000      20.000000       0.000000\n", "50%        47.000000       1.065000      89.000000       0.000000\n", "75%       122.000000       1.066000     220.000000       0.000000\n", "max       285.000000       1.069000    9990.000000       1.000000"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "code", "execution_count": 10, "id": "832c62e5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1}\n"]}, {"data": {"text/plain": ["<Axes: ylabel='Frequency'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(set(data['类型'].to_list()))\n", "data['类型'].plot.hist()"]}, {"cell_type": "code", "execution_count": 11, "id": "b6c4c7f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: ylabel='Frequency'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# print(set(data['手'].to_list()))\n", "data['手'].plot.hist()"]}, {"cell_type": "code", "execution_count": 12, "id": "96238c08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1.064, 1.063, 1.062, 1.065, 1.061, 1.06, 1.066, 1.067, 1.068, 1.069}\n"]}, {"data": {"text/plain": ["<Axes: ylabel='Frequency'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(set(data['价格'].to_list()))\n", "data['价格'].plot.hist()"]}, {"cell_type": "code", "execution_count": 13, "id": "26b78e17", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:01   2023-09-01 09:30:01\n", "2023-09-01 09:30:01   2023-09-01 09:30:01\n", "2023-09-01 09:30:01   2023-09-01 09:30:01\n", "2023-09-01 09:30:01   2023-09-01 09:30:01\n", "2023-09-01 09:30:01   2023-09-01 09:30:01\n", "                              ...        \n", "2023-09-01 14:59:59   2023-09-01 13:29:59\n", "2023-09-01 14:59:59   2023-09-01 13:29:59\n", "2023-09-01 14:59:59   2023-09-01 13:29:59\n", "2023-09-01 14:59:59   2023-09-01 13:29:59\n", "2023-09-01 14:59:59   2023-09-01 13:29:59\n", "Name: 时间, Length: 354352, dtype: datetime64[ns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["dt = datetime.strptime(date+\" 12:00:00\", \"%Y%m%d %H:%M:%S\")\n", "fix_dt = data.index.to_series().apply(lambda x:x - timedelta(hours=1.5) if x>dt else x)\n", "fix_dt"]}, {"cell_type": "code", "execution_count": 14, "id": "48c92101", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["count    354351.000000\n", "mean          0.040632\n", "std           0.519220\n", "min           0.000000\n", "25%           0.000000\n", "50%           0.000000\n", "75%           0.000000\n", "max          46.000000\n", "Name: 时间, dtype: float64\n"]}, {"data": {"text/plain": ["<Axes: ylabel='Frequency'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 成交频率\n", "diff_sec = fix_dt.diff().dt.total_seconds()\n", "print(diff_sec.describe())\n", "diff_sec.plot.hist()"]}, {"cell_type": "code", "execution_count": 15, "id": "35da9b96", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: ylabel='Frequency'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["diff_sec2 = diff_sec.apply(lambda x: 2 if x>2 else x)\n", "diff_sec2.plot.hist()"]}, {"cell_type": "markdown", "id": "fbe376ae", "metadata": {}, "source": ["# 数据整理"]}, {"cell_type": "code", "execution_count": 16, "id": "80917b28", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>价格</th>\n", "      <th>手</th>\n", "      <th>类型</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>1.0</td>\n", "      <td>1.064</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>2.0</td>\n", "      <td>1.064</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>3.0</td>\n", "      <td>1.064</td>\n", "      <td>20.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>4.0</td>\n", "      <td>1.064</td>\n", "      <td>150.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>5.0</td>\n", "      <td>1.064</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>4.0</td>\n", "      <td>1.066</td>\n", "      <td>1349.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>5.0</td>\n", "      <td>1.067</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>6.0</td>\n", "      <td>1.066</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:29:59</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:01</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>354354 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                      序号     价格       手   类型\n", "时间                                          \n", "2023-09-01 09:30:01  1.0  1.064    10.0  1.0\n", "2023-09-01 09:30:01  2.0  1.064     5.0  1.0\n", "2023-09-01 09:30:01  3.0  1.064    20.0  1.0\n", "2023-09-01 09:30:01  4.0  1.064   150.0  1.0\n", "2023-09-01 09:30:01  5.0  1.064    10.0  1.0\n", "...                  ...    ...     ...  ...\n", "2023-09-01 14:59:59  4.0  1.066  1349.0  1.0\n", "2023-09-01 14:59:59  5.0  1.067    50.0  0.0\n", "2023-09-01 14:59:59  6.0  1.066   100.0  1.0\n", "2023-09-01 09:29:59  NaN    NaN     NaN  NaN\n", "2023-09-01 15:00:01  NaN    NaN     NaN  NaN\n", "\n", "[354354 rows x 4 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# if len(data) == 0:\n", "#     return None\n", "\n", "# 添加空行，确保每个切片都有重采样数据\n", "date_str = str(data.index[0])[:11]\n", "data.loc[pd.to_datetime(date_str + \"09:29:59\")] = np.nan\n", "data.loc[pd.to_datetime(date_str + \"15:00:01\")] = np.nan\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 17, "id": "8a3c378d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>区间成交</th>\n", "      <th>区间成买</th>\n", "      <th>区间成卖</th>\n", "      <th>区间手数</th>\n", "      <th>区间买手</th>\n", "      <th>区间卖手</th>\n", "      <th>区间金额</th>\n", "      <th>区间买额</th>\n", "      <th>区间卖额</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:00</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1778</td>\n", "      <td>1054</td>\n", "      <td>724</td>\n", "      <td>441502.0</td>\n", "      <td>315506.0</td>\n", "      <td>125996.0</td>\n", "      <td>469800.836</td>\n", "      <td>335748.464</td>\n", "      <td>134052.372</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>4009</td>\n", "      <td>3922</td>\n", "      <td>87</td>\n", "      <td>514334.0</td>\n", "      <td>506504.0</td>\n", "      <td>7830.0</td>\n", "      <td>547355.546</td>\n", "      <td>539032.256</td>\n", "      <td>8323.290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>557</td>\n", "      <td>466</td>\n", "      <td>91</td>\n", "      <td>105657.0</td>\n", "      <td>102427.0</td>\n", "      <td>3230.0</td>\n", "      <td>112415.818</td>\n", "      <td>108982.328</td>\n", "      <td>3433.490</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1170</td>\n", "      <td>902</td>\n", "      <td>268</td>\n", "      <td>365348.0</td>\n", "      <td>351421.0</td>\n", "      <td>13927.0</td>\n", "      <td>388909.209</td>\n", "      <td>374104.744</td>\n", "      <td>14804.465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>69</td>\n", "      <td>12</td>\n", "      <td>57</td>\n", "      <td>25675.0</td>\n", "      <td>1962.0</td>\n", "      <td>23713.0</td>\n", "      <td>27371.512</td>\n", "      <td>2093.454</td>\n", "      <td>25278.058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>1456.0</td>\n", "      <td>1.0</td>\n", "      <td>1455.0</td>\n", "      <td>1552.097</td>\n", "      <td>1.067</td>\n", "      <td>1551.030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>17</td>\n", "      <td>13</td>\n", "      <td>4</td>\n", "      <td>607.0</td>\n", "      <td>571.0</td>\n", "      <td>36.0</td>\n", "      <td>647.633</td>\n", "      <td>609.257</td>\n", "      <td>38.376</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>37</td>\n", "      <td>7</td>\n", "      <td>30</td>\n", "      <td>10612.0</td>\n", "      <td>310.0</td>\n", "      <td>10302.0</td>\n", "      <td>11312.702</td>\n", "      <td>330.770</td>\n", "      <td>10981.932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:03</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6602 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                     区间成交  区间成买  区间成卖      区间手数      区间买手      区间卖手  \\\n", "时间                                                                    \n", "2023-09-01 09:30:00     1     0     0       0.0       0.0       0.0   \n", "2023-09-01 09:30:03  1778  1054   724  441502.0  315506.0  125996.0   \n", "2023-09-01 09:30:06  4009  3922    87  514334.0  506504.0    7830.0   \n", "2023-09-01 09:30:09   557   466    91  105657.0  102427.0    3230.0   \n", "2023-09-01 09:30:12  1170   902   268  365348.0  351421.0   13927.0   \n", "...                   ...   ...   ...       ...       ...       ...   \n", "2023-09-01 14:59:51    69    12    57   25675.0    1962.0   23713.0   \n", "2023-09-01 14:59:54    10     1     9    1456.0       1.0    1455.0   \n", "2023-09-01 14:59:57    17    13     4     607.0     571.0      36.0   \n", "2023-09-01 15:00:00    37     7    30   10612.0     310.0   10302.0   \n", "2023-09-01 15:00:03     1     0     0       0.0       0.0       0.0   \n", "\n", "                           区间金额        区间买额        区间卖额  \n", "时间                                                       \n", "2023-09-01 09:30:00       0.000       0.000       0.000  \n", "2023-09-01 09:30:03  469800.836  335748.464  134052.372  \n", "2023-09-01 09:30:06  547355.546  539032.256    8323.290  \n", "2023-09-01 09:30:09  112415.818  108982.328    3433.490  \n", "2023-09-01 09:30:12  388909.209  374104.744   14804.465  \n", "...                         ...         ...         ...  \n", "2023-09-01 14:59:51   27371.512    2093.454   25278.058  \n", "2023-09-01 14:59:54    1552.097       1.067    1551.030  \n", "2023-09-01 14:59:57     647.633     609.257      38.376  \n", "2023-09-01 15:00:00   11312.702     330.770   10981.932  \n", "2023-09-01 15:00:03       0.000       0.000       0.000  \n", "\n", "[6602 rows x 9 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 订单数量\n", "data[\"区间成交\"] = 1\n", "data[\"区间成买\"] = data[\"类型\"].apply(lambda x: 1 if x == B else 0)\n", "data[\"区间成卖\"] = data[\"类型\"].apply(lambda x: 1 if x == S else 0)\n", "\n", "# 手数\n", "data[\"区间手数\"] = data[\"手\"]\n", "data[\"区间买手\"] = data[\"手\"] * data[\"区间成买\"]\n", "data[\"区间卖手\"] = data[\"手\"] * data[\"区间成卖\"]\n", "\n", "# 额\n", "data[\"区间金额\"] = data[\"手\"] * data[\"价格\"]\n", "data[\"区间买额\"] = data[\"区间买手\"] * data[\"价格\"]\n", "data[\"区间卖额\"] = data[\"区间卖手\"] * data[\"价格\"]\n", "\n", "std_data = data.resample(\"3S\", closed=\"right\", label=\"right\").apply(\n", "    {\n", "        \"区间成交\": np.sum,\n", "        \"区间成买\": np.sum,\n", "        \"区间成卖\": np.sum,\n", "        \"区间手数\": np.sum,\n", "        \"区间买手\": np.sum,\n", "        \"区间卖手\": np.sum,\n", "        \"区间金额\": np.sum,\n", "        \"区间买额\": np.sum,\n", "        \"区间卖额\": np.sum,\n", "    }\n", ")\n", "\n", "std_data"]}, {"cell_type": "code", "execution_count": 18, "id": "f74cdea3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>区间成交</th>\n", "      <th>区间成买</th>\n", "      <th>区间成卖</th>\n", "      <th>区间手数</th>\n", "      <th>区间买手</th>\n", "      <th>区间卖手</th>\n", "      <th>区间金额</th>\n", "      <th>区间买额</th>\n", "      <th>区间卖额</th>\n", "      <th>累计成交</th>\n", "      <th>...</th>\n", "      <th>区间VWAP</th>\n", "      <th>累计VWAP</th>\n", "      <th>区间VWAP买</th>\n", "      <th>累计VWAP买</th>\n", "      <th>区间VWAP卖</th>\n", "      <th>累计VWAP卖</th>\n", "      <th>区间买卖比</th>\n", "      <th>区间买卖手比</th>\n", "      <th>累计买卖比</th>\n", "      <th>累计买卖手比</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1778</td>\n", "      <td>1054</td>\n", "      <td>724</td>\n", "      <td>441502.0</td>\n", "      <td>315506.0</td>\n", "      <td>125996.0</td>\n", "      <td>469800.836</td>\n", "      <td>335748.464</td>\n", "      <td>134052.372</td>\n", "      <td>1778</td>\n", "      <td>...</td>\n", "      <td>1.064097</td>\n", "      <td>1.064097</td>\n", "      <td>1.064159</td>\n", "      <td>1.064159</td>\n", "      <td>1.063941</td>\n", "      <td>1.063941</td>\n", "      <td>1.455801</td>\n", "      <td>2.504095</td>\n", "      <td>1.455801</td>\n", "      <td>2.504095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>4009</td>\n", "      <td>3922</td>\n", "      <td>87</td>\n", "      <td>514334.0</td>\n", "      <td>506504.0</td>\n", "      <td>7830.0</td>\n", "      <td>547355.546</td>\n", "      <td>539032.256</td>\n", "      <td>8323.290</td>\n", "      <td>5787</td>\n", "      <td>...</td>\n", "      <td>1.064203</td>\n", "      <td>1.064154</td>\n", "      <td>1.064221</td>\n", "      <td>1.064197</td>\n", "      <td>1.063000</td>\n", "      <td>1.063886</td>\n", "      <td>45.080460</td>\n", "      <td>64.687612</td>\n", "      <td>6.135635</td>\n", "      <td>6.142379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>557</td>\n", "      <td>466</td>\n", "      <td>91</td>\n", "      <td>105657.0</td>\n", "      <td>102427.0</td>\n", "      <td>3230.0</td>\n", "      <td>112415.818</td>\n", "      <td>108982.328</td>\n", "      <td>3433.490</td>\n", "      <td>6344</td>\n", "      <td>...</td>\n", "      <td>1.063969</td>\n", "      <td>1.064135</td>\n", "      <td>1.064000</td>\n", "      <td>1.064175</td>\n", "      <td>1.063000</td>\n", "      <td>1.063866</td>\n", "      <td>5.120879</td>\n", "      <td>31.711146</td>\n", "      <td>6.033259</td>\n", "      <td>6.744958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1170</td>\n", "      <td>902</td>\n", "      <td>268</td>\n", "      <td>365348.0</td>\n", "      <td>351421.0</td>\n", "      <td>13927.0</td>\n", "      <td>388909.209</td>\n", "      <td>374104.744</td>\n", "      <td>14804.465</td>\n", "      <td>7514</td>\n", "      <td>...</td>\n", "      <td>1.064490</td>\n", "      <td>1.064226</td>\n", "      <td>1.064549</td>\n", "      <td>1.064278</td>\n", "      <td>1.063005</td>\n", "      <td>1.063786</td>\n", "      <td>3.365672</td>\n", "      <td>25.233072</td>\n", "      <td>5.422222</td>\n", "      <td>8.450342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>710</td>\n", "      <td>399</td>\n", "      <td>311</td>\n", "      <td>112293.0</td>\n", "      <td>89978.0</td>\n", "      <td>22315.0</td>\n", "      <td>119457.437</td>\n", "      <td>95736.592</td>\n", "      <td>23720.845</td>\n", "      <td>8224</td>\n", "      <td>...</td>\n", "      <td>1.063801</td>\n", "      <td>1.064195</td>\n", "      <td>1.064000</td>\n", "      <td>1.064260</td>\n", "      <td>1.063000</td>\n", "      <td>1.063685</td>\n", "      <td>1.282958</td>\n", "      <td>4.032176</td>\n", "      <td>4.553005</td>\n", "      <td>7.881430</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>1139.0</td>\n", "      <td>603.0</td>\n", "      <td>536.0</td>\n", "      <td>1214.777</td>\n", "      <td>643.401</td>\n", "      <td>571.376</td>\n", "      <td>354219</td>\n", "      <td>...</td>\n", "      <td>1.066529</td>\n", "      <td>1.064677</td>\n", "      <td>1.067000</td>\n", "      <td>1.064787</td>\n", "      <td>1.066000</td>\n", "      <td>1.064145</td>\n", "      <td>0.428571</td>\n", "      <td>1.125000</td>\n", "      <td>4.839320</td>\n", "      <td>4.843962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>69</td>\n", "      <td>12</td>\n", "      <td>57</td>\n", "      <td>25675.0</td>\n", "      <td>1962.0</td>\n", "      <td>23713.0</td>\n", "      <td>27371.512</td>\n", "      <td>2093.454</td>\n", "      <td>25278.058</td>\n", "      <td>354288</td>\n", "      <td>...</td>\n", "      <td>1.066076</td>\n", "      <td>1.064677</td>\n", "      <td>1.067000</td>\n", "      <td>1.064787</td>\n", "      <td>1.066000</td>\n", "      <td>1.064147</td>\n", "      <td>0.210526</td>\n", "      <td>0.082739</td>\n", "      <td>4.834975</td>\n", "      <td>4.838164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>1456.0</td>\n", "      <td>1.0</td>\n", "      <td>1455.0</td>\n", "      <td>1552.097</td>\n", "      <td>1.067</td>\n", "      <td>1551.030</td>\n", "      <td>354298</td>\n", "      <td>...</td>\n", "      <td>1.066001</td>\n", "      <td>1.064677</td>\n", "      <td>1.067000</td>\n", "      <td>1.064787</td>\n", "      <td>1.066000</td>\n", "      <td>1.064147</td>\n", "      <td>0.111111</td>\n", "      <td>0.000687</td>\n", "      <td>4.834275</td>\n", "      <td>4.837803</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>17</td>\n", "      <td>13</td>\n", "      <td>4</td>\n", "      <td>607.0</td>\n", "      <td>571.0</td>\n", "      <td>36.0</td>\n", "      <td>647.633</td>\n", "      <td>609.257</td>\n", "      <td>38.376</td>\n", "      <td>354315</td>\n", "      <td>...</td>\n", "      <td>1.066941</td>\n", "      <td>1.064677</td>\n", "      <td>1.067000</td>\n", "      <td>1.064787</td>\n", "      <td>1.066000</td>\n", "      <td>1.064147</td>\n", "      <td>3.250000</td>\n", "      <td>15.861111</td>\n", "      <td>4.834170</td>\n", "      <td>4.837823</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>37</td>\n", "      <td>7</td>\n", "      <td>30</td>\n", "      <td>10612.0</td>\n", "      <td>310.0</td>\n", "      <td>10302.0</td>\n", "      <td>11312.702</td>\n", "      <td>330.770</td>\n", "      <td>10981.932</td>\n", "      <td>354352</td>\n", "      <td>...</td>\n", "      <td>1.066029</td>\n", "      <td>1.064677</td>\n", "      <td>1.067000</td>\n", "      <td>1.064787</td>\n", "      <td>1.066000</td>\n", "      <td>1.064148</td>\n", "      <td>0.233333</td>\n", "      <td>0.030091</td>\n", "      <td>4.831899</td>\n", "      <td>4.835281</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 28 columns</p>\n", "</div>"], "text/plain": ["                     区间成交  区间成买  区间成卖      区间手数      区间买手      区间卖手  \\\n", "时间                                                                    \n", "2023-09-01 09:30:03  1778  1054   724  441502.0  315506.0  125996.0   \n", "2023-09-01 09:30:06  4009  3922    87  514334.0  506504.0    7830.0   \n", "2023-09-01 09:30:09   557   466    91  105657.0  102427.0    3230.0   \n", "2023-09-01 09:30:12  1170   902   268  365348.0  351421.0   13927.0   \n", "2023-09-01 09:30:15   710   399   311  112293.0   89978.0   22315.0   \n", "...                   ...   ...   ...       ...       ...       ...   \n", "2023-09-01 14:59:48    10     3     7    1139.0     603.0     536.0   \n", "2023-09-01 14:59:51    69    12    57   25675.0    1962.0   23713.0   \n", "2023-09-01 14:59:54    10     1     9    1456.0       1.0    1455.0   \n", "2023-09-01 14:59:57    17    13     4     607.0     571.0      36.0   \n", "2023-09-01 15:00:00    37     7    30   10612.0     310.0   10302.0   \n", "\n", "                           区间金额        区间买额        区间卖额    累计成交  ...  \\\n", "时间                                                               ...   \n", "2023-09-01 09:30:03  469800.836  335748.464  134052.372    1778  ...   \n", "2023-09-01 09:30:06  547355.546  539032.256    8323.290    5787  ...   \n", "2023-09-01 09:30:09  112415.818  108982.328    3433.490    6344  ...   \n", "2023-09-01 09:30:12  388909.209  374104.744   14804.465    7514  ...   \n", "2023-09-01 09:30:15  119457.437   95736.592   23720.845    8224  ...   \n", "...                         ...         ...         ...     ...  ...   \n", "2023-09-01 14:59:48    1214.777     643.401     571.376  354219  ...   \n", "2023-09-01 14:59:51   27371.512    2093.454   25278.058  354288  ...   \n", "2023-09-01 14:59:54    1552.097       1.067    1551.030  354298  ...   \n", "2023-09-01 14:59:57     647.633     609.257      38.376  354315  ...   \n", "2023-09-01 15:00:00   11312.702     330.770   10981.932  354352  ...   \n", "\n", "                       区间VWAP    累计VWAP   区间VWAP买   累计VWAP买   区间VWAP卖  \\\n", "时间                                                                      \n", "2023-09-01 09:30:03  1.064097  1.064097  1.064159  1.064159  1.063941   \n", "2023-09-01 09:30:06  1.064203  1.064154  1.064221  1.064197  1.063000   \n", "2023-09-01 09:30:09  1.063969  1.064135  1.064000  1.064175  1.063000   \n", "2023-09-01 09:30:12  1.064490  1.064226  1.064549  1.064278  1.063005   \n", "2023-09-01 09:30:15  1.063801  1.064195  1.064000  1.064260  1.063000   \n", "...                       ...       ...       ...       ...       ...   \n", "2023-09-01 14:59:48  1.066529  1.064677  1.067000  1.064787  1.066000   \n", "2023-09-01 14:59:51  1.066076  1.064677  1.067000  1.064787  1.066000   \n", "2023-09-01 14:59:54  1.066001  1.064677  1.067000  1.064787  1.066000   \n", "2023-09-01 14:59:57  1.066941  1.064677  1.067000  1.064787  1.066000   \n", "2023-09-01 15:00:00  1.066029  1.064677  1.067000  1.064787  1.066000   \n", "\n", "                      累计VWAP卖      区间买卖比     区间买卖手比     累计买卖比    累计买卖手比  \n", "时间                                                                       \n", "2023-09-01 09:30:03  1.063941   1.455801   2.504095  1.455801  2.504095  \n", "2023-09-01 09:30:06  1.063886  45.080460  64.687612  6.135635  6.142379  \n", "2023-09-01 09:30:09  1.063866   5.120879  31.711146  6.033259  6.744958  \n", "2023-09-01 09:30:12  1.063786   3.365672  25.233072  5.422222  8.450342  \n", "2023-09-01 09:30:15  1.063685   1.282958   4.032176  4.553005  7.881430  \n", "...                       ...        ...        ...       ...       ...  \n", "2023-09-01 14:59:48  1.064145   0.428571   1.125000  4.839320  4.843962  \n", "2023-09-01 14:59:51  1.064147   0.210526   0.082739  4.834975  4.838164  \n", "2023-09-01 14:59:54  1.064147   0.111111   0.000687  4.834275  4.837803  \n", "2023-09-01 14:59:57  1.064147   3.250000  15.861111  4.834170  4.837823  \n", "2023-09-01 15:00:00  1.064148   0.233333   0.030091  4.831899  4.835281  \n", "\n", "[4800 rows x 28 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# 删除最后一行 第一行\n", "std_data = std_data[1:-1]\n", "\n", "# 删除 11:30:00 - 13:00:00 之间的数据（不含）\n", "am = std_data.between_time(\"09:30:01\", \"11:30:00\")\n", "pm = std_data.between_time(\"13:00:01\", \"15:00:00\")\n", "std_data = std_data.loc[am.index.union(pm.index), :]\n", "\n", "# 增加累计数据\n", "std_data[\"累计成交\"] = std_data[\"区间成交\"].cumsum()\n", "std_data[\"累计成买\"] = std_data[\"区间成买\"].cumsum()\n", "std_data[\"累计成卖\"] = std_data[\"区间成卖\"].cumsum()\n", "std_data[\"累计手数\"] = std_data[\"区间手数\"].cumsum()\n", "std_data[\"累计买手\"] = std_data[\"区间买手\"].cumsum()\n", "std_data[\"累计卖手\"] = std_data[\"区间卖手\"].cumsum()\n", "std_data[\"累计金额\"] = std_data[\"区间金额\"].cumsum()\n", "std_data[\"累计买额\"] = std_data[\"区间买额\"].cumsum()\n", "std_data[\"累计卖额\"] = std_data[\"区间卖额\"].cumsum()\n", "\n", "# ['区间VWAP', '累计VWAP', '区间VWAP买', '累计VWAP买', '区间VWAP卖', '累计VWAP卖', '区间买卖比', '区间买卖手比', '累计买卖比', '累计买卖手比']\n", "std_data[\"区间VWAP\"] = std_data[\"区间金额\"] / std_data[\"区间手数\"]\n", "std_data[\"累计VWAP\"] = std_data[\"累计金额\"] / std_data[\"累计手数\"]\n", "std_data[\"区间VWAP买\"] = std_data[\"区间买额\"] / std_data[\"区间买手\"]\n", "std_data[\"累计VWAP买\"] = std_data[\"累计买额\"] / std_data[\"累计买手\"]\n", "std_data[\"区间VWAP卖\"] = std_data[\"区间卖额\"] / std_data[\"区间卖手\"]\n", "std_data[\"累计VWAP卖\"] = std_data[\"累计卖额\"] / std_data[\"累计卖手\"]\n", "\n", "std_data[\"区间买卖比\"] = std_data[\"区间成买\"] / std_data[\"区间成卖\"]\n", "std_data[\"区间买卖手比\"] = std_data[\"区间买手\"] / std_data[\"区间卖手\"]\n", "std_data[\"累计买卖比\"] = std_data[\"累计成买\"] / std_data[\"累计成卖\"]\n", "std_data[\"累计买卖手比\"] = std_data[\"累计买手\"] / std_data[\"累计卖手\"]\n", "\n", "# 填充 nan\n", "std_data = std_data.fillna(-1)\n", "std_data"]}, {"cell_type": "code", "execution_count": 19, "id": "760426d0", "metadata": {}, "outputs": [], "source": ["def top_deal(_type, begin_dt, end_dt, data):\n", "    _type_name = \"买\" if _type ==B else \"卖\"\n", "    \n", "    data_pv = data.iloc[:-2, 1:4]\n", "    # data_pv = data.dropna().iloc[:, 1:4]\n", "\n", "    # 选取时间范围内的交易\n", "    dt = data_pv.index.to_series()\n", "    _trade = data_pv[(dt>begin_dt) & (dt<=end_dt) & data_pv['类型']==_type].sort_values('手', ascending=False).iloc[:100,:2].reset_index(drop=True)\n", "\n", "    # 添加到足够的数量\n", "    for i in range(100):\n", "         if i not in _trade.index:\n", "            _trade.loc[i] = 0\n", "\n", "    # 转置到一行中\n", "    p = _trade[['价格']].T.reset_index(drop=True)\n", "    p.columns = [f'成{_type_name}{i+1}价' for i in range(100)]\n", "    v = _trade[['手']].T.reset_index(drop=True)\n", "    v.columns = [f'成{_type_name}{i+1}手' for i in range(100)]\n", "\n", "    # 合并\n", "    _top_deal = pd.concat([p, v], axis=1)\n", "\n", "    # 排序\n", "    columns = list(_top_deal)\n", "    fix_columns = []\n", "    for i in range(100):\n", "        fix_columns.append(columns[i])\n", "        fix_columns.append(columns[i+100])\n", "\n", "    _top_deal = _top_deal[fix_columns]\n", "    \n", "    # 统一时间索引\n", "    _top_deal['时间'] = end_dt\n", "    _top_deal.set_index('时间', inplace=True)\n", "\n", "    return _top_deal"]}, {"cell_type": "code", "execution_count": 20, "id": "c8e8732d", "metadata": {}, "outputs": [], "source": ["# %%timeit\n", "# top_deal(B, '买', begin_dt, end_dt, data)"]}, {"cell_type": "code", "execution_count": 21, "id": "de111bcf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-09-01 09:30:00 - 2023-09-01 09:30:03\n", "2023-09-01 09:55:00 - 2023-09-01 09:55:03\n", "2023-09-01 10:20:00 - 2023-09-01 10:20:03\n", "2023-09-01 10:45:00 - 2023-09-01 10:45:03\n", "2023-09-01 11:10:00 - 2023-09-01 11:10:03\n", "2023-09-01 13:05:00 - 2023-09-01 13:05:03\n", "2023-09-01 13:30:00 - 2023-09-01 13:30:03\n", "2023-09-01 13:55:00 - 2023-09-01 13:55:03\n", "2023-09-01 14:20:00 - 2023-09-01 14:20:03\n", "2023-09-01 14:45:00 - 2023-09-01 14:45:03\n", "CPU times: total: 11min 31s\n", "Wall time: 11min 31s\n"]}], "source": ["%%time\n", "# 区间内成交量大到小排序前100笔买卖交易 价格/数量\n", "top_data = pd.DataFrame()\n", "for idx, end_dt in enumerate(std_data.index):\n", "    begin_dt = end_dt- <PERSON><PERSON><PERSON>(seconds=3)\n", "    \n", "    if idx%500 == 0:\n", "        print(f'{begin_dt} - {end_dt}')\n", "    \n", "    top_data = pd.concat([top_data, pd.concat([top_deal(B, begin_dt, end_dt, data), top_deal(S, begin_dt, end_dt, data)], axis=1)])\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 22, "id": "ca4fee63", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>成买1价</th>\n", "      <th>成买1手</th>\n", "      <th>成买2价</th>\n", "      <th>成买2手</th>\n", "      <th>成买3价</th>\n", "      <th>成买3手</th>\n", "      <th>成买4价</th>\n", "      <th>成买4手</th>\n", "      <th>成买5价</th>\n", "      <th>成买5手</th>\n", "      <th>...</th>\n", "      <th>成卖96价</th>\n", "      <th>成卖96手</th>\n", "      <th>成卖97价</th>\n", "      <th>成卖97手</th>\n", "      <th>成卖98价</th>\n", "      <th>成卖98手</th>\n", "      <th>成卖99价</th>\n", "      <th>成卖99手</th>\n", "      <th>成卖100价</th>\n", "      <th>成卖100手</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>1.064</td>\n", "      <td>500.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>1.063</td>\n", "      <td>88.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>1.065</td>\n", "      <td>9990.0</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 400 columns</p>\n", "</div>"], "text/plain": ["                      成买1价    成买1手   成买2价    成买2手   成买3价    成买3手   成买4价  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 09:30:06  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 09:30:09  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 09:30:12  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 09:30:15  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "...                    ...     ...    ...     ...    ...     ...    ...   \n", "2023-09-01 14:59:48  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 14:59:51  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 14:59:54  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 14:59:57  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "2023-09-01 15:00:00  1.065  9990.0  1.065  9990.0  1.065  9990.0  1.065   \n", "\n", "                       成买4手   成买5价    成买5手  ...  成卖96价  成卖96手  成卖97价  成卖97手  \\\n", "时间                                          ...                               \n", "2023-09-01 09:30:03  9990.0  1.065  9990.0  ...  1.064  500.0  1.064  200.0   \n", "2023-09-01 09:30:06  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 09:30:09  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 09:30:12  9990.0  1.065  9990.0  ...  1.063   88.0  1.063   71.0   \n", "2023-09-01 09:30:15  9990.0  1.065  9990.0  ...  1.063   52.0  1.063   52.0   \n", "...                     ...    ...     ...  ...    ...    ...    ...    ...   \n", "2023-09-01 14:59:48  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 14:59:51  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 14:59:54  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 14:59:57  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "2023-09-01 15:00:00  9990.0  1.065  9990.0  ...  0.000    0.0  0.000    0.0   \n", "\n", "                     成卖98价  成卖98手  成卖99价  成卖99手  成卖100价  成卖100手  \n", "时间                                                               \n", "2023-09-01 09:30:03  1.064  200.0  1.064  200.0   1.064   200.0  \n", "2023-09-01 09:30:06  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 09:30:09  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 09:30:12  1.063   71.0  1.063   71.0   1.063    71.0  \n", "2023-09-01 09:30:15  1.063   52.0  1.063   52.0   1.063    52.0  \n", "...                    ...    ...    ...    ...     ...     ...  \n", "2023-09-01 14:59:48  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 14:59:51  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 14:59:54  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 14:59:57  0.000    0.0  0.000    0.0   0.000     0.0  \n", "2023-09-01 15:00:00  0.000    0.0  0.000    0.0   0.000     0.0  \n", "\n", "[4800 rows x 400 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["top_data"]}, {"cell_type": "code", "execution_count": null, "id": "23b6333a", "metadata": {}, "outputs": [], "source": ["# 合并数据\n", "std_data = pd.concat([std_data, top_data], axis=1)\n", "std_data"]}, {"cell_type": "code", "execution_count": 30, "id": "ccdc9b92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ZBCJ_区间成交</th>\n", "      <th>ZBCJ_区间成买</th>\n", "      <th>ZBCJ_区间成卖</th>\n", "      <th>ZBCJ_区间手数</th>\n", "      <th>ZBCJ_区间买手</th>\n", "      <th>ZBCJ_区间卖手</th>\n", "      <th>ZBCJ_区间金额</th>\n", "      <th>ZBCJ_区间买额</th>\n", "      <th>ZBCJ_区间卖额</th>\n", "      <th>ZBCJ_累计成交</th>\n", "      <th>...</th>\n", "      <th>ZBCJ_成卖96价</th>\n", "      <th>ZBCJ_成卖96手</th>\n", "      <th>ZBCJ_成卖97价</th>\n", "      <th>ZBCJ_成卖97手</th>\n", "      <th>ZBCJ_成卖98价</th>\n", "      <th>ZBCJ_成卖98手</th>\n", "      <th>ZBCJ_成卖99价</th>\n", "      <th>ZBCJ_成卖99手</th>\n", "      <th>ZBCJ_成卖100价</th>\n", "      <th>ZBCJ_成卖100手</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1778</td>\n", "      <td>1054</td>\n", "      <td>724</td>\n", "      <td>441502.0</td>\n", "      <td>315506.0</td>\n", "      <td>125996.0</td>\n", "      <td>469800.836</td>\n", "      <td>335748.464</td>\n", "      <td>134052.372</td>\n", "      <td>1778</td>\n", "      <td>...</td>\n", "      <td>1.064</td>\n", "      <td>500.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "      <td>1.064</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>4009</td>\n", "      <td>3922</td>\n", "      <td>87</td>\n", "      <td>514334.0</td>\n", "      <td>506504.0</td>\n", "      <td>7830.0</td>\n", "      <td>547355.546</td>\n", "      <td>539032.256</td>\n", "      <td>8323.290</td>\n", "      <td>5787</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>557</td>\n", "      <td>466</td>\n", "      <td>91</td>\n", "      <td>105657.0</td>\n", "      <td>102427.0</td>\n", "      <td>3230.0</td>\n", "      <td>112415.818</td>\n", "      <td>108982.328</td>\n", "      <td>3433.490</td>\n", "      <td>6344</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1170</td>\n", "      <td>902</td>\n", "      <td>268</td>\n", "      <td>365348.0</td>\n", "      <td>351421.0</td>\n", "      <td>13927.0</td>\n", "      <td>388909.209</td>\n", "      <td>374104.744</td>\n", "      <td>14804.465</td>\n", "      <td>7514</td>\n", "      <td>...</td>\n", "      <td>1.063</td>\n", "      <td>88.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "      <td>1.063</td>\n", "      <td>71.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>710</td>\n", "      <td>399</td>\n", "      <td>311</td>\n", "      <td>112293.0</td>\n", "      <td>89978.0</td>\n", "      <td>22315.0</td>\n", "      <td>119457.437</td>\n", "      <td>95736.592</td>\n", "      <td>23720.845</td>\n", "      <td>8224</td>\n", "      <td>...</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "      <td>1.063</td>\n", "      <td>52.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>1139.0</td>\n", "      <td>603.0</td>\n", "      <td>536.0</td>\n", "      <td>1214.777</td>\n", "      <td>643.401</td>\n", "      <td>571.376</td>\n", "      <td>354219</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>69</td>\n", "      <td>12</td>\n", "      <td>57</td>\n", "      <td>25675.0</td>\n", "      <td>1962.0</td>\n", "      <td>23713.0</td>\n", "      <td>27371.512</td>\n", "      <td>2093.454</td>\n", "      <td>25278.058</td>\n", "      <td>354288</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>1456.0</td>\n", "      <td>1.0</td>\n", "      <td>1455.0</td>\n", "      <td>1552.097</td>\n", "      <td>1.067</td>\n", "      <td>1551.030</td>\n", "      <td>354298</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>17</td>\n", "      <td>13</td>\n", "      <td>4</td>\n", "      <td>607.0</td>\n", "      <td>571.0</td>\n", "      <td>36.0</td>\n", "      <td>647.633</td>\n", "      <td>609.257</td>\n", "      <td>38.376</td>\n", "      <td>354315</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>37</td>\n", "      <td>7</td>\n", "      <td>30</td>\n", "      <td>10612.0</td>\n", "      <td>310.0</td>\n", "      <td>10302.0</td>\n", "      <td>11312.702</td>\n", "      <td>330.770</td>\n", "      <td>10981.932</td>\n", "      <td>354352</td>\n", "      <td>...</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 428 columns</p>\n", "</div>"], "text/plain": ["                     ZBCJ_区间成交  ZBCJ_区间成买  ZBCJ_区间成卖  ZBCJ_区间手数  ZBCJ_区间买手  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03       1778       1054        724   441502.0   315506.0   \n", "2023-09-01 09:30:06       4009       3922         87   514334.0   506504.0   \n", "2023-09-01 09:30:09        557        466         91   105657.0   102427.0   \n", "2023-09-01 09:30:12       1170        902        268   365348.0   351421.0   \n", "2023-09-01 09:30:15        710        399        311   112293.0    89978.0   \n", "...                        ...        ...        ...        ...        ...   \n", "2023-09-01 14:59:48         10          3          7     1139.0      603.0   \n", "2023-09-01 14:59:51         69         12         57    25675.0     1962.0   \n", "2023-09-01 14:59:54         10          1          9     1456.0        1.0   \n", "2023-09-01 14:59:57         17         13          4      607.0      571.0   \n", "2023-09-01 15:00:00         37          7         30    10612.0      310.0   \n", "\n", "                     ZBCJ_区间卖手   ZBCJ_区间金额   ZBCJ_区间买额   ZBCJ_区间卖额  ZBCJ_累计成交  \\\n", "时间                                                                              \n", "2023-09-01 09:30:03   125996.0  469800.836  335748.464  134052.372       1778   \n", "2023-09-01 09:30:06     7830.0  547355.546  539032.256    8323.290       5787   \n", "2023-09-01 09:30:09     3230.0  112415.818  108982.328    3433.490       6344   \n", "2023-09-01 09:30:12    13927.0  388909.209  374104.744   14804.465       7514   \n", "2023-09-01 09:30:15    22315.0  119457.437   95736.592   23720.845       8224   \n", "...                        ...         ...         ...         ...        ...   \n", "2023-09-01 14:59:48      536.0    1214.777     643.401     571.376     354219   \n", "2023-09-01 14:59:51    23713.0   27371.512    2093.454   25278.058     354288   \n", "2023-09-01 14:59:54     1455.0    1552.097       1.067    1551.030     354298   \n", "2023-09-01 14:59:57       36.0     647.633     609.257      38.376     354315   \n", "2023-09-01 15:00:00    10302.0   11312.702     330.770   10981.932     354352   \n", "\n", "                     ...  ZBCJ_成卖96价  ZBCJ_成卖96手  ZBCJ_成卖97价  ZBCJ_成卖97手  \\\n", "时间                   ...                                                   \n", "2023-09-01 09:30:03  ...       1.064       500.0       1.064       200.0   \n", "2023-09-01 09:30:06  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 09:30:09  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 09:30:12  ...       1.063        88.0       1.063        71.0   \n", "2023-09-01 09:30:15  ...       1.063        52.0       1.063        52.0   \n", "...                  ...         ...         ...         ...         ...   \n", "2023-09-01 14:59:48  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:51  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:54  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:57  ...       0.000         0.0       0.000         0.0   \n", "2023-09-01 15:00:00  ...       0.000         0.0       0.000         0.0   \n", "\n", "                     ZBCJ_成卖98价  ZBCJ_成卖98手  ZBCJ_成卖99价  ZBCJ_成卖99手  \\\n", "时间                                                                    \n", "2023-09-01 09:30:03       1.064       200.0       1.064       200.0   \n", "2023-09-01 09:30:06       0.000         0.0       0.000         0.0   \n", "2023-09-01 09:30:09       0.000         0.0       0.000         0.0   \n", "2023-09-01 09:30:12       1.063        71.0       1.063        71.0   \n", "2023-09-01 09:30:15       1.063        52.0       1.063        52.0   \n", "...                         ...         ...         ...         ...   \n", "2023-09-01 14:59:48       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:51       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:54       0.000         0.0       0.000         0.0   \n", "2023-09-01 14:59:57       0.000         0.0       0.000         0.0   \n", "2023-09-01 15:00:00       0.000         0.0       0.000         0.0   \n", "\n", "                     ZBCJ_成卖100价  ZBCJ_成卖100手  \n", "时间                                             \n", "2023-09-01 09:30:03        1.064        200.0  \n", "2023-09-01 09:30:06        0.000          0.0  \n", "2023-09-01 09:30:09        0.000          0.0  \n", "2023-09-01 09:30:12        1.063         71.0  \n", "2023-09-01 09:30:15        1.063         52.0  \n", "...                          ...          ...  \n", "2023-09-01 14:59:48        0.000          0.0  \n", "2023-09-01 14:59:51        0.000          0.0  \n", "2023-09-01 14:59:54        0.000          0.0  \n", "2023-09-01 14:59:57        0.000          0.0  \n", "2023-09-01 15:00:00        0.000          0.0  \n", "\n", "[4800 rows x 428 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# 添加前缀\n", "std_data.columns = [f'ZBCJ_{i}' for i in list(std_data)]\n", "std_data"]}, {"cell_type": "code", "execution_count": 31, "id": "e8300c7d", "metadata": {}, "outputs": [], "source": ["std_data.to_csv(\"std_data_zbcj.csv\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}