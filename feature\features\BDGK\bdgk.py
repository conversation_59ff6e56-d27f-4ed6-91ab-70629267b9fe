import pandas as pd
import numpy as np

from ...base_class import time_point_feature


class bdgk_base(time_point_feature):
    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """
        # %处理
        unit_cols_0 = ['涨幅', '振幅', '换手']
        # 判断列名 与 unit_cols_0是否有交集
        un = list(set(unit_cols_0).intersection(set(list(data))))
        data.loc[:, un] = data.loc[:, un].replace("%", "", regex=True)

        # 单位处理
        unit_cols_1 = ['总量', '总额', '内盘', '外盘']
        un = list(set(unit_cols_1).intersection(set(list(data))))
        for col in un:
            data[col] = data[col].map(
                lambda x: 10000 * (float(x.replace("万", "")))
                if "万" in str(x)
                else 1e8 * (float(x.replace("亿", "")))
                if "亿" in str(x)
                else float(x)
            )

        # - 处理
        unit_cols_2 = ['每笔', '总笔']
        un = list(set(unit_cols_2).intersection(set(list(data))))
        for col in un:
            data[col] = data[col].map(
                lambda x: np.nan if "-" in str(x) else float(x))

        return data


class bdgk_001(bdgk_base):
    """
    1 + 17
    '时间' + ['现价', '今开', '涨跌', '最高', '涨幅', '最低', '昨收', '均价', '振幅', '量比', '总量', '总额', '总笔', '每笔', '涨停', '跌停', '内外盘差']
    """

    def need_raw_cols(self):
        return [
            '时间',
            '现价',
            '今开',
            '涨跌',
            '最高',
            '涨幅',
            '最低',
            '昨收',
            '均价',
            '振幅',
            '量比',
            '总量',
            '总额',
            '总笔',
            '每笔',
            '外盘',
            '内盘',
            '涨停',
            '跌停',
        ]

    def feature_check(self, data: pd.DataFrame):
        return True

    def raw_name(self):
        return "标的概况"

    def name(self):
        """
        返回特征名称
        """
        return "标的概况"

    def version(self):
        return "001_20230911"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 内外盘历史数据不正确，差额正确
        data.eval('内外盘差=外盘-内盘', inplace=True)

        # 删除 内外盘 列
        data.drop(['内盘', '外盘'], axis=1, inplace=True)

        return data


class bdgk_002(bdgk_base):
    """
    复现研报特征:
    20131211-民生证券-CTA程序化交易实务研究之六：基于机器学习的订单簿高频交易策略

        14. 最新价  
            最新的撮合成交价
        15. 成交量  
            当天累计的成交量
        16. 成交量对数差

    # 以下特征不在此计算
        1. 买一价 
        2. 卖一价
        3. 买一量
        4. 卖一量
        5. 买一对数收益率  
            相邻两个买一价格的对数差
        6. 卖一对数收益率:  
            相邻两个卖一价格的对数差
        7. 相对价差  
            价差/((买一价+卖一价)/2)
        8. 买一量对数差  
            和上一个买一量的对数差
        9. 卖一量对数差  
            和上一个卖一量的对数差
        10. 斜率  
            价差/深度
        11. 深度  
            (买一量+卖一量 )/2

    # 以下特征不计算
        12. 持仓量
        13. 持仓量对数差
        17. 基差
    """

    def need_raw_cols(self):
        return [
            '时间',
            '现价',
            '总量',
        ]

    def feature_check(self, data: pd.DataFrame):
        return True

    def raw_name(self):
        return "标的概况"

    def name(self):
        """
        返回特征名称
        """
        return "标的概况"

    def version(self):
        return "002_20231027"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 14. 最新价
        # 最新的撮合成交价
        data['price'] = data['现价']

        # 15. 成交量
        # 当天累计的成交量
        data['vol'] = data['总量']

        # 16. 成交量对数差
        data['vol_log_ret'] = np.log(data['vol']/data['vol'].shift())

        return data.loc[:, [
            'price',
            'vol',
            'vol_log_ret'
        ]].fillna(0)


class bdgk_003(bdgk_base):
    def need_raw_cols(self):
        return [
            '时间',
            '量比',
            '换手',
        ]

    def feature_check(self, data: pd.DataFrame):
        return True

    def raw_name(self):
        return "标的概况"

    def name(self):
        """
        返回特征名称
        """
        return "标的概况"

    def version(self):
        return "003_20240821"

    def format_unit(self, data: pd.DataFrame):
        super().format_unit(data)

        # 全部转为 float
        data = data.astype(float)
        
        return data.fillna(0.0)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 添加前缀
        data.columns = [f'BDGK_{i}' for i in list(data)]
        return data
