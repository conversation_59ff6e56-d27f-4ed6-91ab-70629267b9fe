{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c420bf00", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from tool import get_time_point\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ff9ad752", "metadata": {}, "outputs": [], "source": ["date = '20230901'"]}, {"cell_type": "code", "execution_count": 3, "id": "f26285f2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 0 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [2023-09-01 09:30:03, 2023-09-01 09:30:06, 2023-09-01 09:30:09, 2023-09-01 09:30:12, 2023-09-01 09:30:15, 2023-09-01 09:30:18, 2023-09-01 09:30:21, 2023-09-01 09:30:24, 2023-09-01 09:30:27, 2023-09-01 09:30:30, 2023-09-01 09:30:33, 2023-09-01 09:30:36, 2023-09-01 09:30:39, 2023-09-01 09:30:42, 2023-09-01 09:30:45, 2023-09-01 09:30:48, 2023-09-01 09:30:51, 2023-09-01 09:30:54, 2023-09-01 09:30:57, 2023-09-01 09:31:00, 2023-09-01 09:31:03, 2023-09-01 09:31:06, 2023-09-01 09:31:09, 2023-09-01 09:31:12, 2023-09-01 09:31:15, 2023-09-01 09:31:18, 2023-09-01 09:31:21, 2023-09-01 09:31:24, 2023-09-01 09:31:27, 2023-09-01 09:31:30, 2023-09-01 09:31:33, 2023-09-01 09:31:36, 2023-09-01 09:31:39, 2023-09-01 09:31:42, 2023-09-01 09:31:45, 2023-09-01 09:31:48, 2023-09-01 09:31:51, 2023-09-01 09:31:54, 2023-09-01 09:31:57, 2023-09-01 09:32:00, 2023-09-01 09:32:03, 2023-09-01 09:32:06, 2023-09-01 09:32:09, 2023-09-01 09:32:12, 2023-09-01 09:32:15, 2023-09-01 09:32:18, 2023-09-01 09:32:21, 2023-09-01 09:32:24, 2023-09-01 09:32:27, 2023-09-01 09:32:30, 2023-09-01 09:32:33, 2023-09-01 09:32:36, 2023-09-01 09:32:39, 2023-09-01 09:32:42, 2023-09-01 09:32:45, 2023-09-01 09:32:48, 2023-09-01 09:32:51, 2023-09-01 09:32:54, 2023-09-01 09:32:57, 2023-09-01 09:33:00, 2023-09-01 09:33:03, 2023-09-01 09:33:06, 2023-09-01 09:33:09, 2023-09-01 09:33:12, 2023-09-01 09:33:15, 2023-09-01 09:33:18, 2023-09-01 09:33:21, 2023-09-01 09:33:24, 2023-09-01 09:33:27, 2023-09-01 09:33:30, 2023-09-01 09:33:33, 2023-09-01 09:33:36, 2023-09-01 09:33:39, 2023-09-01 09:33:42, 2023-09-01 09:33:45, 2023-09-01 09:33:48, 2023-09-01 09:33:51, 2023-09-01 09:33:54, 2023-09-01 09:33:57, 2023-09-01 09:34:00, 2023-09-01 09:34:03, 2023-09-01 09:34:06, 2023-09-01 09:34:09, 2023-09-01 09:34:12, 2023-09-01 09:34:15, 2023-09-01 09:34:18, 2023-09-01 09:34:21, 2023-09-01 09:34:24, 2023-09-01 09:34:27, 2023-09-01 09:34:30, 2023-09-01 09:34:33, 2023-09-01 09:34:36, 2023-09-01 09:34:39, 2023-09-01 09:34:42, 2023-09-01 09:34:45, 2023-09-01 09:34:48, 2023-09-01 09:34:51, 2023-09-01 09:34:54, 2023-09-01 09:34:57, 2023-09-01 09:35:00, ...]\n", "\n", "[4800 rows x 0 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["time_point = get_time_point(date)\n", "time_point"]}, {"cell_type": "code", "execution_count": 23, "id": "be90164f", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03    1.0\n", "2023-09-01 09:30:06    1.0\n", "2023-09-01 09:30:09    1.0\n", "2023-09-01 09:30:12    1.0\n", "2023-09-01 09:30:15    1.0\n", "                      ... \n", "2023-09-01 14:59:48    8.0\n", "2023-09-01 14:59:51    8.0\n", "2023-09-01 14:59:54    8.0\n", "2023-09-01 14:59:57    8.0\n", "2023-09-01 15:00:00    8.0\n", "Name: 30min_idx, Length: 4800, dtype: float64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 所属的30min序号，第几个30min\n", "time_point[\"30min_idx\"] = (time_point.index - pd.Timedelta(1, 's') - pd.to_datetime(date + \" 09:30:00\")).total_seconds() // (60 * 30) + 1\n", "time_point[\"30min_idx\"] = time_point[\"30min_idx\"].apply(lambda x: x if x<5 else x-3)\n", "time_point[\"30min_idx\"] "]}, {"cell_type": "code", "execution_count": 24, "id": "38f193cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='时间'>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 26102 (\\N{CJK UNIFIED IDEOGRAPH-65F6}) missing from current font.\n", "  func(*args, **kwargs)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 38388 (\\N{CJK UNIFIED IDEOGRAPH-95F4}) missing from current font.\n", "  func(*args, **kwargs)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 26102 (\\N{CJK UNIFIED IDEOGRAPH-65F6}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 38388 (\\N{CJK UNIFIED IDEOGRAPH-95F4}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["time_point[\"30min_idx\"].plot()"]}, {"cell_type": "code", "execution_count": 25, "id": "6e6b37ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:59:33    1.0\n", "2023-09-01 09:59:36    1.0\n", "2023-09-01 09:59:39    1.0\n", "2023-09-01 09:59:42    1.0\n", "2023-09-01 09:59:45    1.0\n", "2023-09-01 09:59:48    1.0\n", "2023-09-01 09:59:51    1.0\n", "2023-09-01 09:59:54    1.0\n", "2023-09-01 09:59:57    1.0\n", "2023-09-01 10:00:00    1.0\n", "2023-09-01 10:00:03    2.0\n", "2023-09-01 10:00:06    2.0\n", "2023-09-01 10:00:09    2.0\n", "2023-09-01 10:00:12    2.0\n", "2023-09-01 10:00:15    2.0\n", "2023-09-01 10:00:18    2.0\n", "2023-09-01 10:00:21    2.0\n", "2023-09-01 10:00:24    2.0\n", "2023-09-01 10:00:27    2.0\n", "2023-09-01 10:00:30    2.0\n", "2023-09-01 10:00:33    2.0\n", "2023-09-01 10:00:36    2.0\n", "2023-09-01 10:00:39    2.0\n", "2023-09-01 10:00:42    2.0\n", "2023-09-01 10:00:45    2.0\n", "2023-09-01 10:00:48    2.0\n", "2023-09-01 10:00:51    2.0\n", "2023-09-01 10:00:54    2.0\n", "2023-09-01 10:00:57    2.0\n", "2023-09-01 10:01:00    2.0\n", "2023-09-01 10:01:03    2.0\n", "2023-09-01 10:01:06    2.0\n", "2023-09-01 10:01:09    2.0\n", "2023-09-01 10:01:12    2.0\n", "2023-09-01 10:01:15    2.0\n", "2023-09-01 10:01:18    2.0\n", "2023-09-01 10:01:21    2.0\n", "2023-09-01 10:01:24    2.0\n", "2023-09-01 10:01:27    2.0\n", "2023-09-01 10:01:30    2.0\n", "Name: 30min_idx, dtype: float64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["time_point[\"30min_idx\"][590:630]"]}, {"cell_type": "code", "execution_count": 26, "id": "73bb9e80", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03    5\n", "2023-09-01 09:30:06    5\n", "2023-09-01 09:30:09    5\n", "2023-09-01 09:30:12    5\n", "2023-09-01 09:30:15    5\n", "                      ..\n", "2023-09-01 14:59:48    5\n", "2023-09-01 14:59:51    5\n", "2023-09-01 14:59:54    5\n", "2023-09-01 14:59:57    5\n", "2023-09-01 15:00:00    5\n", "Name: weekday, Length: 4800, dtype: int64"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# 时点所属的星期\n", "time_point[\"weekday\"] = time_point.index.weekday + 1\n", "time_point[\"weekday\"]"]}, {"cell_type": "code", "execution_count": 27, "id": "94846e52", "metadata": {}, "outputs": [{"data": {"text/plain": ["时间\n", "2023-09-01 09:30:03   -2.449294e-16\n", "2023-09-01 09:30:06   -2.449294e-16\n", "2023-09-01 09:30:09   -2.449294e-16\n", "2023-09-01 09:30:12   -2.449294e-16\n", "2023-09-01 09:30:15   -2.449294e-16\n", "                           ...     \n", "2023-09-01 14:59:48   -2.449294e-16\n", "2023-09-01 14:59:51   -2.449294e-16\n", "2023-09-01 14:59:54   -2.449294e-16\n", "2023-09-01 14:59:57   -2.449294e-16\n", "2023-09-01 15:00:00   -2.449294e-16\n", "Name: weekday_sin, Length: 4800, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["time_point['weekday_sin'] = np.sin(time_point[\"weekday\"]*(2.*np.pi/5))\n", "time_point['weekday_sin']"]}, {"cell_type": "code", "execution_count": 28, "id": "a6e9098b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='时间'>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 26102 (\\N{CJK UNIFIED IDEOGRAPH-65F6}) missing from current font.\n", "  func(*args, **kwargs)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 38388 (\\N{CJK UNIFIED IDEOGRAPH-95F4}) missing from current font.\n", "  func(*args, **kwargs)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 26102 (\\N{CJK UNIFIED IDEOGRAPH-65F6}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "D:\\programs\\anaconda3\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 38388 (\\N{CJK UNIFIED IDEOGRAPH-95F4}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["time_point['weekday_sin'].plot()"]}, {"cell_type": "code", "execution_count": 37, "id": "f8b863e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>weekdata</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    weekdata\n", "0          1\n", "1          2\n", "2          3\n", "3          4\n", "4          5\n", "5          1\n", "6          2\n", "7          3\n", "8          4\n", "9          5\n", "10         1\n", "11         2\n", "12         3\n", "13         4\n", "14         5\n", "15         1\n", "16         2\n", "17         3\n", "18         4\n", "19         5\n", "20         1\n", "21         2\n", "22         3\n", "23         4\n", "24         5"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["week = [i+1 for i in range(5)] * 5\n", "week_df = pd.DataFrame(week, columns=['weekdata'])\n", "week_df"]}, {"cell_type": "code", "execution_count": 46, "id": "36e7449b", "metadata": {}, "outputs": [{"data": {"text/plain": ["0     9.510565e-01\n", "1     5.877853e-01\n", "2    -5.877853e-01\n", "3    -9.510565e-01\n", "4    -2.449294e-16\n", "5     9.510565e-01\n", "6     5.877853e-01\n", "7    -5.877853e-01\n", "8    -9.510565e-01\n", "9    -2.449294e-16\n", "10    9.510565e-01\n", "11    5.877853e-01\n", "12   -5.877853e-01\n", "13   -9.510565e-01\n", "14   -2.449294e-16\n", "15    9.510565e-01\n", "16    5.877853e-01\n", "17   -5.877853e-01\n", "18   -9.510565e-01\n", "19   -2.449294e-16\n", "20    9.510565e-01\n", "21    5.877853e-01\n", "22   -5.877853e-01\n", "23   -9.510565e-01\n", "24   -2.449294e-16\n", "Name: weekday_sin, dtype: float64"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["week_df['weekday_sin'] = np.sin(week_df[\"weekdata\"]*(2.*np.pi/5))\n", "week_df['weekday_sin']"]}, {"cell_type": "code", "execution_count": 47, "id": "d8b4ce30", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["week_df['weekday_sin'].plot()"]}, {"cell_type": "code", "execution_count": null, "id": "84c65995", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d2a59006", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}