from abc import ABC, abstractmethod

import pandas as pd
import numpy as np

from ...tool import drop_duplicate_row
from ...base_class import mongo_base


class depth_base(mongo_base):
    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    def name(self):
        """
        返回特征名称
        """
        return "depth"

    def need_cols(self):
        return [
            'bid1_price',
            'bid2_price',
            'bid3_price',
            'bid4_price',
            'bid5_price',
            'bid6_price',
            'bid7_price',
            'bid8_price',
            'bid9_price',
            'bid10_price',
            'bid1_vol',
            'bid2_vol',
            'bid3_vol',
            'bid4_vol',
            'bid5_vol',
            'bid6_vol',
            'bid7_vol',
            'bid8_vol',
            'bid9_vol',
            'bid10_vol',
            'ask1_price',
            'ask2_price',
            'ask3_price',
            'ask4_price',
            'ask5_price',
            'ask6_price',
            'ask7_price',
            'ask8_price',
            'ask9_price',
            'ask10_price',
            'ask1_vol',
            'ask2_vol',
            'ask3_vol',
            'ask4_vol',
            'ask5_vol',
            'ask6_vol',
            'ask7_vol',
            'ask8_vol',
            'ask9_vol',
            'ask10_vol',
            'save_timestamp']

    @abstractmethod
    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        return data

    def get(self, code, mongo_client, _begin_timestamp, _end_timestamp):
        """
        获取指定标的的深度数据
        """
        filter = {}
        for i in self.need_cols():
            filter[i] = 1

        if 'save_timestamp' not in filter:
            filter['save_timestamp'] = 1

        if "id" not in filter:
            filter["id"] = 1

        # 获取数据
        data = mongo_client['binance']['depth'].find(
            {'symbol': code.lower(), 'save_timestamp': {
                '$gte': _begin_timestamp * 1000, '$lt': _end_timestamp * 1000}}, filter
        )
        data = pd.DataFrame(list(data))
        if len(data) == 0:
            return None

        # 去重
        data = drop_duplicate_row(data)

        # 删除id重复
        data.drop_duplicates(subset='id', keep='first', inplace=True)

        # 格式化时间
        data['save_timestamp'] = pd.to_datetime(
            data['save_timestamp'], origin='1970-01-01 08:00:00', unit='ms')

        return self.after_std_format(data)


class depth_001(depth_base):
    def __init__(self, kwargs: dict = {}) -> None:
        """
        """
        super().__init__(kwargs)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        datetime | 特征列 标签 (不包含标的名称)
        ['卖1价', '卖1量', '买1价', '买1量', '卖2价', '卖2量', '买2价', '买2量', '卖3价', '卖3量', '买3价', '买3量', '卖4价', '卖4量', '买4价', '买4量', '卖5价', '卖5量', '买5价', '买5量', '卖6价', '卖6量', '买6价', '买6量', '卖7价', '卖7量', '买7价', '买7量', '卖8价', '卖8量', '买8价', '买8量', '卖9价', '卖9量', '买9价', '买9量', '卖10价', '卖10量', '买10价', '买10量']
        """
        # 重命名列
        data.rename(columns={
            'bid1_price': '买1价',
            'bid2_price': '买2价',
            'bid3_price': '买3价',
            'bid4_price': '买4价',
            'bid5_price': '买5价',
            'bid6_price': '买6价',
            'bid7_price': '买7价',
            'bid8_price': '买8价',
            'bid9_price': '买9价',
            'bid10_price': '买10价',
            'bid1_vol': '买1量',
            'bid2_vol': '买2量',
            'bid3_vol': '买3量',
            'bid4_vol': '买4量',
            'bid5_vol': '买5量',
            'bid6_vol': '买6量',
            'bid7_vol': '买7量',
            'bid8_vol': '买8量',
            'bid9_vol': '买9量',
            'bid10_vol': '买10量',
            'ask1_price': '卖1价',
            'ask2_price': '卖2价',
            'ask3_price': '卖3价',
            'ask4_price': '卖4价',
            'ask5_price': '卖5价',
            'ask6_price': '卖6价',
            'ask7_price': '卖7价',
            'ask8_price': '卖8价',
            'ask9_price': '卖9价',
            'ask10_price': '卖10价',
            'ask1_vol': '卖1量',
            'ask2_vol': '卖2量',
            'ask3_vol': '卖3量',
            'ask4_vol': '卖4量',
            'ask5_vol': '卖5量',
            'ask6_vol': '卖6量',
            'ask7_vol': '卖7量',
            'ask8_vol': '卖8量',
            'ask9_vol': '卖9量',
            'ask10_vol': '卖10量'
        }, inplace=True)

        # 排序列名
        data = data.loc[:,
                        ['id', 'save_timestamp', '卖1价', '卖1量', '买1价', '买1量', '卖2价', '卖2量', '买2价', '买2量', '卖3价', '卖3量', '买3价', '买3量', '卖4价', '卖4量', '买4价', '买4量', '卖5价', '卖5量', '买5价', '买5量',
                            '卖6价', '卖6量', '买6价', '买6量', '卖7价', '卖7量', '买7价', '买7量', '卖8价', '卖8量', '买8价', '买8量', '卖9价', '卖9量', '买9价', '买9量', '卖10价', '卖10量', '买10价', '买10量']
                        ]

        # 数字货币的行情连续，在这里计算会损失训练数据
        # # 中间价格
        # middle_price = (data['卖1价'] + data['买1价']) / 2

        # # 获取参数
        # d1 = self.kwargs['d1']
        # ma = self.kwargs['ma']

        # for _d in d1:
        #     data_ma = middle_price.rolling(
        #         ma).mean().rolling(ma).mean().shift(-ma)
        #     rets = data_ma.pct_change(_d).shift(-_d)

        #     # 标签
        #     data[f'target_{_d}'] = rets.apply(
        #         lambda x: 1 if x > 0 else -1 if x < 0 else np.nan if np.isnan(x) else 0)

        return data
