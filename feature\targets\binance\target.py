from abc import ABC, abstractmethod
import pandas as pd
import numpy as np

from ...features.binance.depth import depth_base


class target_001(depth_base):
    """
    预测周期 d1
    阈值 a = 0.00001
    平均周期 ma = 200
    {'d1':3, 'a':0.00001, 'ma':200}
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "001_20240319"

    def name(self):
        """
        返回特征名称
        """
        return "标签"

    def need_cols(self):
        return [
            'bid1_price',
            'ask1_price',
            'save_timestamp']

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 中间价格
        middle_price = (data['bid1_price'] + data['ask1_price']) / 2

        # 获取参数
        d1 = self.kwargs['d1']
        ma = self.kwargs['ma']
        a = self.kwargs['a']

        data_ma = middle_price.rolling(ma).mean().rolling(ma).mean().shift(-ma)
        rets = data_ma.pct_change(d1).shift(-d1)

        # 标签
        data['target'] = rets.apply(
            lambda x: 1 if x > a else -1 if x < -a else np.nan if np.isnan(x) else 0)

        return data
