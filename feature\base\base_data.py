import pandas as pd
import numpy as np
import os

from ..base_class import time_point_feature
from ..config import get_his_dates
from ..tool import std_3s_time_data


class sdpk_base(time_point_feature):

    def raw_name(self):
        return "十档盘口"

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """
        # 单位处理
        unit_cols_1 = ['总卖', '总买']
        un = list(set(unit_cols_1).intersection(set(data.columns)))
        data.loc[:, un] = data.loc[:, un].applymap(
            lambda x: 10000 * (float(x.replace("万", "")))
            if "万" in str(x)
            else 1e8 * (float(x.replace("亿", "")))
            if "亿" in str(x)
            else float(x)
        )

        return data
