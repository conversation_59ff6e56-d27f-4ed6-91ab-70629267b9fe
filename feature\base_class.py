from abc import ABC, abstractmethod
import os
import pandas as pd
import numpy as np
from datetime import datetime
import pickle
import time
from tqdm import tqdm
import pymongo
from pymongo import MongoClient

from .tool import get_time_point, get_dates, std_3s_time_data, balance_label, get_his_raw_data
from .tool import tz_beijing
from .config import get_realtime_data_path, get_his_data_path, get_his_dates, get_realtime_dates, pre_codes, params, his_root

from py_ext.wechat import send_wx

class base(ABC):
    def __init__(self, kwargs: dict = {}) -> None:
        self.kwargs = kwargs

    @abstractmethod
    def name(self):
        """
        返回特征名称
        """

    @abstractmethod
    def version(self):
        """
        返回特征版本号
        001_20230911
        """


class mongo_base(base):
    @abstractmethod
    def get(self, code, mongo_client, _begin_timestamp, _end_timestamp):
        """
        返回数据
        """

    @abstractmethod
    def need_cols(self):
        """
        返回需要的列名列表
        """

    def name(self):
        """
        返回特征名称
        """
        return "no use"

    def version(self):
        """
        返回特征版本号
        001_20230911
        """
        return "no use"


class date_split_symbol_base(base):
    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

        self.code = None
        self.date = None
        self.his = True

    @abstractmethod
    def get(self, code, date, his: bool):
        """
        返回数据
        """
        self.date = date
        self.code = code
        self.his = his

        # print(
        #     f"获取 {self.name()} {code} {date} {'his' if his else 'realtime'} 数据。。。")


class time_feature(date_split_symbol_base):
    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    @abstractmethod
    def add_time_feature(self, date: str, time_point: pd.DataFrame):
        """增加时间特征"""

    def get(self, code, date, his: bool):
        """
        返回时间特征数据
        date: 20230901

        code, his 无用
        """
        super().get(code, date, his)

        # 检查本地是否已经存在
        path = r'featrue_data/tdx_a_datas/time_feature/' if params.kaggle else f'D:/code/featrue_data/tdx_a_datas/time_feature/'

        # 构造参数字符串
        args_str = "_".join([f"{i}{self.kwargs[i]}" for i in self.kwargs])

        file = path + f'{date}_{self.version()}'
        if args_str:
            file += f'_{args_str}.pkl'
        else:
            file += '.pkl'

        if os.path.exists(file):
            return pd.read_pickle(file)

        # 获取标准时间戳
        time_point = get_time_point(date)

        # 构造时间特征数据
        time_feature = self.add_time_feature(time_point)

        if not os.path.exists(path):
            os.makedirs(path)

        # 保存特征数据
        time_feature.to_pickle(file)
        return time_feature


class feature(date_split_symbol_base):
    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    @abstractmethod
    def cal(self, data: pd.DataFrame):
        """
        计算特征数据
        返回标准时间索引的特征数据
        """

    @staticmethod
    def raw_check(file: str):
        """
        原始数据的检查
        返回正常/矫正后的文件路径

        1. 读取第一行作为列名，其他行的列数必须与第一行相同，若不同返回 false
        2. 列名重复出现的删除重复的行，修改 file 源文件
        3. 如果出现空白的行，删除，修改file 源文件

        """

        # 读取数据
        raws = []
        with open(file, "r", encoding="gbk") as f:
            raws = f.readlines()

        # 标志是否需要重写
        rewrite_dup = False
        rewrite_dot = False

        # 检查是否空白行
        # 重复的标题行
        dump_idx = []
        for i in range(1, len(raws)):
            if raws[i] == "\n":
                dump_idx.append(i)

            elif raws[i] == raws[0]:
                dump_idx.append(i)

        # 倒叙删除 dump_idx 中索引的项
        for i in dump_idx[::-1]:
            rewrite_dup = True
            del raws[i]

        # 检查数据列数 与列名是否匹配
        # col 列数
        raw_cols = len(raws[0].split(","))
        # 遍历rows 如何列表的长度大于 row_cols 则返回false
        for i in range(1, len(raws)):
            part_list = raws[i].split(",")
            data_parts = len(part_list)
            
            # 多了一个 , 
            if data_parts - 1 == raw_cols:
                if raws[i][-1] == ',':
                    rewrite_dot = True
                    raws[i] = raws[i][:-1]
                    continue
                elif raws[i][-2:] == ',\n':
                    rewrite_dot = True
                    raws[i] = raws[i].replace(',\n', '\n')
                    continue
            
            if (data_parts != raw_cols):
                # 逐笔成交.csv 集合均价 缺少 B/S 字段
                if '逐笔成交.csv' in file and data_parts + 1 == raw_cols:
                    if part_list[0] in ['09:25:00', '15:00:00']:
                        continue

                msg = f'{file} 数据列数不匹配,请检查'
                print(msg)
                send_wx(msg)
                return ""

        if not rewrite_dot and not rewrite_dup:
            return file

        # 重写文件
        new_file = f'tempfile_{os.getpid()}'
        with open(new_file, "w", encoding="gbk") as f:
            msg = '重写原始文件\n'
            if rewrite_dup:
                msg += f'删除行:\n{dump_idx}'
            if rewrite_dot:
                msg += '去除尾部逗号'

            print(msg)
            f.writelines(raws)

        return new_file

    @abstractmethod
    def feature_check(self, data: pd.DataFrame):
        """
        特征数据的检查
        返回数据是否符合要求
        """

    @abstractmethod
    def raw_name(self):
        """
        返回特征原始文件名称
        """

    def get(self, code, date, his: bool, save: bool = True):
        """
        获取特征数据
        code: 513050
        date: 20230901
        """
        assert his, "暂时只支持历史数据"
        super().get(code, date, his)

        # 获取原始数据
        data = get_his_raw_data(
            code, date, self.raw_name(), feature.raw_check)

        # 原始数据异常处理
        if None is data:
            return None

        # 时间列标准化处理
        _time_str = data.iloc[0]["时间"]
        if len(_time_str) == 8 and ':' in _time_str:
            # 补充日期部分
            data["时间"] = data["时间"].apply(
                lambda x: f'{date[:4]}-{date[4:6]}-{date[6:]} {x}')

        # 计算特征数据
        data = self.cal(data)

        # 检查特征数据
        if (not None is data) and self.feature_check(data):
            return data

        return None


class time_point_feature(feature):
    """时刻切片特征"""

    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    @abstractmethod
    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """

    @abstractmethod
    def need_raw_cols(self):
        """
        返回需要的原始数据列名
        """

    @abstractmethod
    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """

    def std_format(self, data: pd.DataFrame):
        _need_cols = self.need_raw_cols()
        if '时间' not in _need_cols:
            _need_cols.append('时间')

        data = data.loc[:, _need_cols]

        # 删除重复的行
        data.drop_duplicates(inplace=True)
        data.drop_duplicates(subset='时间', keep="last", inplace=True)

        # 删除 时间 列中 "时间未更新" 的行
        # 适配旧的历史数据
        data = data.query('时间 != "时间未更新"').reset_index(drop=True)

        # 格式化时间
        date = data["时间"][0][:10]
        data["时间"] = pd.to_datetime(data["时间"])
        data.set_index("时间", inplace=True)
        data = data.between_time("09:30:00", "14:55:00")

        # 格式化
        data = self.format_unit(data)

        # 交易时间内的数据数量检查
        if len(data) == 0:
            return None
        
        # 全部转为 float
        data = data.astype(float)

        # 标准话时间
        data = std_3s_time_data(data)

        ###########################
        # # 标准化时间
        # dts = get_time_point(date)
        # dts["std_dts"] = 1

        # # 按照合并 data dts
        # data = pd.merge(data, dts, how="outer", left_index=True,
        #                 right_index=True, sort=True)

        # # 对除了 std_dts 之外的列进行向后填充
        # std_dts = data["std_dts"].copy()
        # data.fillna(method="ffill", inplace=True)
        # data["std_dts"] = std_dts

        # # 标准时间的行
        # data.query("std_dts == 1", inplace=True)

        # # 删除 std_dts 列
        # data.drop("std_dts", axis=1, inplace=True)
        ###########################

        return data

    def cal(self, data: pd.DataFrame):
        """
        计算特征数据
        返回标准时间索引的特征数据
        """
        _data = data.copy()
        try:
            # 标准处理
            data = self.std_format(data)
            # 后续处理
            return self.after_std_format(data)
        except Exception as e:
            print(f"{self.name()} {self.code} {self.date} 数据处理异常", flush=True)
            pickle.dump(_data, open("error_cal_data", "wb"))
            raise e

class zb_base(feature):
    """逐笔基类"""

    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    @abstractmethod
    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据
        """

    @abstractmethod
    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""

    def cal(self, data: pd.DataFrame):
        _data = data.copy()
        try:
            data = self.format_unit(data)
            return self.to_time_point(data)
        except Exception as e:
            print(f"{self.name()} {self.code} {self.date} 数据处理异常", flush=True)
            pickle.dump(_data, open("error_cal_data", "wb"))
            raise e
    def feature_check(self, data: pd.DataFrame):
        return True

class zb_feature(zb_base):
    """逐笔特征"""

    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    @abstractmethod
    def top_func(self, end_dt: str, data: pd.DataFrame, top_n: int):
        """
        返回关于end_dt 的 top_n 统计结果
        """

    def add_top_n_data(self, std_time_point_data: pd.DataFrame, data: pd.DataFrame):
        """
        添加 top_n 数据
        std_time_point_data: 时刻数据
        data: 原始逐笔数据
        """

        # 不在此处处理nan/inf，在训练数据标准化后替换为 -1 （nan/inf 不参与标准化）
        # # 填充 nan
        # std_time_point_data = std_time_point_data.fillna(-1)
        # # 将inf替换为-1
        # std_time_point_data.replace([np.inf, -np.inf], -1, inplace=True)

        # 将inf和-inf替换为NaN test:df.columns[df.isnull().any() | df.isin([np.inf, -np.inf]).any()].tolist()
        std_time_point_data.replace([np.inf, -np.inf], np.nan, inplace=True)

        if self.kwargs["top_n"] <= 0:
            return std_time_point_data

        # 区间内成交量大到小排序前100笔买卖交易 价格/数量
        top_data = pd.DataFrame()

        # 添加进度条
        # for end_dt in tqdm(std_time_point_data.index, ncols=80):
        for end_dt in std_time_point_data.index:
            # print(end_dt)
            top_data = pd.concat(
                [top_data, self.top_func(end_dt, data, self.kwargs["top_n"])], axis=0)

        # 合并数据
        return pd.concat([std_time_point_data, top_data], axis=1)


class feature_geter():

    def __init__(self, features: list = [], target=None) -> None:
        """
        features 返回:
            datetime | 特征列 (不包含标的名称)
        target 返回:
            datetime | target/label

        """
        super().__init__()
        self.features = features
        self.target = target
        self.data_path = his_root

    def add_pass_data(self, data: pd.DataFrame, pass_n: int, pass_cols: [list, None]):
        """
        展平前 pass_n 的数据
        排除的列名 ： 时间/target/code
        """

        if None is pass_cols:
            pass_cols = list(data)

        # 排除的列名
        bad_cols = ['时间', 'target', 'code']
        pass_cols = [i for i in pass_cols if i not in bad_cols]

        # 分割数据 不需要/需要展平的数据
        need_pass = data.loc[:, pass_cols]
        no_pass = data.drop(pass_cols, axis=1)

        all_data = need_pass.copy()
        for i in range(1, 1+pass_n):
            # 偏移的数据
            _data = need_pass.shift(i)

            # 重命名列名
            _data.columns = [f'{col}_s{i}' for col in list(_data)]

            # 合并数据
            all_data = pd.concat([all_data, _data], axis=1)

        # 调整列顺序
        cols = list(all_data)
        n2 = int(len(cols)/(pass_n+1))

        cols2 = []
        for i in range(n2):
            for j in range(pass_n+1):
                cols2.append(cols[i+j*n2])

        all_data = all_data[cols2]

        # 合并不需要展平的数据
        data = pd.concat([no_pass, all_data], axis=1)

        return data

    def _produce(self, code, date, his: bool):
        # 新构造生成特征数据
        code_date_feature = pd.DataFrame()
        for _feature in self.features:
            # 逐个计算特征数据
            _data = _feature.get(code, date, his)

            # 如果某个特征数据为空，则跳过
            if None is _data:
                print(_feature.name(), f'{code} {date} 数据为空，跳过。。。')
                return

            assert len(
                _data) == 4700, "每日标准化数据长度应该为 4700 条（09:30:03 - 14:55:00）"

            code_date_feature = pd.concat(
                [code_date_feature, _data], axis=1)

        # 添加标签
        if not None is self.target:
            target_data = self.target.get(
                code, date, his)

            if None is target_data:
                print(self.target.name(), '数据为空，跳过。。。')
                return

            cols = list(target_data)
            if 'target' in cols:
                code_date_feature['target'] = target_data['target']
            elif 'label' in cols:
                code_date_feature['target'] = target_data['label']
            else:
                raise Exception(self.target.name() +
                                '返回数据中没有 target/label 列，请检查')

        # 添加 code 列
        code_date_feature['code'] = code

        # 重置索引
        code_date_feature.reset_index(drop=False, inplace=True)

        return code_date_feature

    def codes_to_list(self, codes):
        if isinstance(codes, str):
            codes = [codes]

        # 记录需要的codes
        global pre_codes
        pre_codes.clear()  # 清空
        pre_codes += codes

        return codes

    def get_feature_desc_str(self):
        # 构造当前选用特征生成器的描述字符串
        feature_desc_str = ''
        all_features = self.features if None is self.target else [
            i for i in self.features] + [self.target]
        for _feature in all_features:
            name = _feature.name()
            v = _feature.version()
            kw = _feature.kwargs
            args_str = "_".join([f"{i}{kw[i]}" for i in kw])
            feature_desc_str += f'{name} | {v} | {args_str}\n'

        return feature_desc_str

    def get_dict_desc(self, path):
        # 读取 特征标签数据 字典
        # {特征描述:文件名}
        # 文件名指向 code_date_feature: DataFrame
        dict_file = path + 'feature_desc'
        dict_desc = {}
        if os.path.exists(dict_file):
            dict_desc = pickle.load(open(dict_file, 'rb'))

        return dict_desc

    def produce(self, codes: [list, str], begin: str, end: str, pass_n: int = 0, pass_cols=None, his=True, dropna=True):
        """
        生成特征数据  
        codes: '513050'/ ['513050', '513051']  
        begin: '20230901'  
        end: '20230901'  
        pass_n: 展平使用前 pass_n 个数据 到每行中  
        pass_cols: 需要展平的列名列表, None 则展平全部列  
        """
        # 转为列表
        codes = self.codes_to_list(codes)

        # 获取对应范围内的数据日期列表
        dates = get_dates(begin, end, his)

        # 获取对应日期的数据
        for code in codes:
            for date in dates:
                self._produce(
                    code, date, his)

                # # 储存数据的路径
                # path = f'D:/code/featrue_data/data/all_data/{code}/{date}/'
                # if not os.path.exists(path):
                #     os.makedirs(path)

                # # 读取 特征标签数据 字典
                # # {特征描述:文件名}
                # # 文件名指向 code_date_feature: DataFrame
                # dict_desc = self.get_dict_desc(path)

                # # 当前选用特征生成器的描述字符串
                # feature_desc_str = self.get_feature_desc_str()

                # # 检查对应日期标的数据
                # if feature_desc_str not in dict_desc:
                #     # 重新计算
                #     self._produce(
                #         dict_desc, feature_desc_str, code, date, his)

    def _get_date_file_name(self, date, path, ext):
        i = 0
        file = os.path.join(path, f'{date}_{i}.{ext}')
        while os.path.exists(file):
            i += 1
            file = os.path.join(path, f'{date}_{i}.{ext}')
        return file

    def get(self, codes: [list, str], begin: str, end: str, pass_cols=None, his=True, dropna=True):
        """
        返回一个pd.DataFrame

        codes: '513050'/ ['513050', '513051']  
        begin: '20230901'  
        end: '20230901'  
        pass_cols: 需要展平的列名列表, None 则展平全部列  
        """
        # 转为列表
        codes = self.codes_to_list(codes)

        # 所有标的日期范围的数据
        feature_data = pd.DataFrame()

        # 获取对应范围内的数据日期列表
        secs = get_dates(begin, end, his)

        # 获取对应日期的数据
        for sec in secs:
            # 当日的数据
            date_feature = pd.DataFrame()
            for code in codes:
                code_date_feature = self._produce(code, sec, his)
                if None is code_date_feature:
                    continue

                # 删除空白行
                if dropna:
                    code_date_feature.dropna(inplace=True)

                # 添加当日数据到标的数据中
                date_feature = pd.concat(
                    [date_feature, code_date_feature], ignore_index=True)

            # 当日数据获取完毕
            # 合并单个标的的数据
            if len(date_feature) > 0:
                feature_data = pd.concat(
                    [feature_data, date_feature], ignore_index=True)

        if len(feature_data) == 0:
            return feature_data

        feature_data.rename(columns={'时间': 'datetime'}, inplace=True)
        return feature_data.sort_values(by=['code', 'datetime']).reset_index(drop=True)

    def get_2d(self, codes: [list, str], begin: str, end: str, pass_n: int = 0, his=True, balance=True, shuffle=True):
        """
        获取特征数据 pass_n的数据仍然保持形状 

        特征数据列表 n*h*w
        其中 n 为一个样本的数据, h为时间维度(pass_n), w为特征维度

        标签数据列表 n*1
        id数据列表 n*1 (code, datetime)

        返回值 (特征数据列表, 标签数据列表, id数据列表)

        codes: '513050'/ ['513050', '513051']  
        begin: '20230901'  
        end: '20230901'  
        pass_n: 展平使用前 pass_n 个数据(不包含当前行)
        balance: 是否样本标签平衡
        shuffle: 是否打乱样本顺序
        """
        # 转为列表
        codes = self.codes_to_list(codes)

        # 所有标的日期范围的数据
        feature_data = []
        target_data = []
        id = []

        # 获取对应范围内的数据日期列表
        dates = get_dates(begin, end, his)

        # 获取对应日期的数据
        for code in codes:
            # 单个标的的数据
            code_feature = []
            code_target = []
            code_ids = []

            for date in dates:
                code_date_feature = self._produce(code, date, his)
                if None is code_date_feature:
                    continue

                # 删除nan行
                code_date_feature.dropna(inplace=True)

                # 截取数据
                # 特征列的索引列表
                feature_idxs = []
                for idx, col in enumerate(list(code_date_feature)):
                    if col not in ['时间', 'target', 'code']:
                        feature_idxs.append(idx)

                for i in range(pass_n, len(code_date_feature)):
                    feature_matrix = code_date_feature.iloc[i -
                                                            pass_n:i, feature_idxs].values
                    code_feature.append(feature_matrix)

                    cur_data = code_date_feature.iloc[i-1]
                    code_target.append(cur_data['target'])

                    code_ids.append(f'{code}_{cur_data["时间"]}')

            # 单个标的数据获取完毕
            # 调整后的样本索引
            balance_idxs = []
            need_change = False

            # 样本标签均衡
            if balance:
                # 平衡后的索引
                target_df = pd.DataFrame(code_target, columns=['target'])
                balance_df = balance_label(target_df)
                balance_idxs = balance_df.index.to_list()
                need_change = True
            else:
                balance_idxs = list(range(len(code_target)))

            # 打乱样本顺序
            if shuffle:
                np.random.shuffle(balance_idxs)
                need_change = True

            # 根据索引过滤数据
            if need_change:
                code_feature = [code_feature[i] for i in balance_idxs]
                code_target = [code_target[i] for i in balance_idxs]
                code_ids = [code_ids[i] for i in balance_idxs]

            # 合并单个标的的数据
            feature_data += code_feature
            target_data += code_target
            id += code_ids

        return feature_data, target_data, id
