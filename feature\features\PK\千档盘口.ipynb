{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ff3639a5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nn 买卖挡 \\n- 价格, 数量  4 * n\\n- 价差,中间价 2 * n\\n- 相邻挡位的价差 2 * n\\n- 买卖均价，买卖均量 4\\n- 均价差，均量差 2\\n- 价量变化率 4 * n\\n- 买价占比，买量占比 2 * n\\n- 价格对数差 2 * n\\n- 相对价差(价差/中间价) n\\n- 深度 (买量+卖量)/2 n\\n- 斜率 (价差/深度) n\\n- 累计成交量 1\\n- 成交量对数差 1\\n\\n量价一阶导，二阶导，资金流，列表击穿，‘spoofing’因子等等\\n\\n20220918-中信建投-“逐鹿”Alpha专题报告（十一）：基于限价订单簿数据的DEEPLOB模型.pdf\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "n 买卖挡 \n", "- 价格, 数量  4 * n\n", "- 价差,中间价 2 * n\n", "- 相邻挡位的价差 2 * n\n", "- 买卖均价，买卖均量 4\n", "- 均价差，均量差 2\n", "- 价量变化率 4 * n\n", "- 买价占比，买量占比 2 * n\n", "- 价格对数差 2 * n\n", "- 相对价差(价差/中间价) n\n", "- 深度 (买量+卖量)/2 n\n", "- 斜率 (价差/深度) n\n", "- 累计成交量 1\n", "- 成交量对数差 1\n", "\n", "量价一阶导，二阶导，资金流，列表击穿，‘spoofing’因子等等\n", "\n", "20220918-中信建投-“逐鹿”Alpha专题报告（十一）：基于限价订单簿数据的DEEPLOB模型.pdf\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 2, "id": "22ca30d3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "id": "50181314", "metadata": {}, "outputs": [], "source": ["# 基础路径\n", "root = r\"D:\\通达信录制数据\\realtime_data\\data\""]}, {"cell_type": "code", "execution_count": 4, "id": "55a0c216", "metadata": {}, "outputs": [], "source": ["# 设置项\n", "date = '20230901'"]}, {"cell_type": "code", "execution_count": 5, "id": "85e1b91d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>涨跌</th>\n", "      <th>涨跌价</th>\n", "      <th>涨跌幅</th>\n", "      <th>时间</th>\n", "      <th>总卖</th>\n", "      <th>卖档数</th>\n", "      <th>卖均量</th>\n", "      <th>卖均价</th>\n", "      <th>总买</th>\n", "      <th>...</th>\n", "      <th>买247笔</th>\n", "      <th>买248价</th>\n", "      <th>买248量</th>\n", "      <th>买248笔</th>\n", "      <th>买249价</th>\n", "      <th>买249量</th>\n", "      <th>买249笔</th>\n", "      <th>买250价</th>\n", "      <th>买250量</th>\n", "      <th>买250笔</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:25:00</td>\n", "      <td>196526</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:30:00</td>\n", "      <td>196526</td>\n", "      <td>93</td>\n", "      <td>156</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:30:01</td>\n", "      <td>196526</td>\n", "      <td>94</td>\n", "      <td>167</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.063</td>\n", "      <td>▲</td>\n", "      <td>0.006</td>\n", "      <td>0.568%</td>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>94</td>\n", "      <td>167</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.063</td>\n", "      <td>▲</td>\n", "      <td>0.006</td>\n", "      <td>0.568%</td>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>94</td>\n", "      <td>171</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15999</th>\n", "      <td>1.067</td>\n", "      <td>▲</td>\n", "      <td>0.010</td>\n", "      <td>0.946%</td>\n", "      <td>14:59:58</td>\n", "      <td>518999</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>275299</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16000</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:58</td>\n", "      <td>517154</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16001</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:59</td>\n", "      <td>517154</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16002</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16003</th>\n", "      <td>1.067</td>\n", "      <td>▲</td>\n", "      <td>0.010</td>\n", "      <td>0.946%</td>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16004 rows × 1513 columns</p>\n", "</div>"], "text/plain": ["          现价 涨跌    涨跌价     涨跌幅        时间      总卖  卖档数  卖均量    卖均价      总买  \\\n", "0      1.064  ▲  0.007  0.662%  09:25:00  196526   93  147  1.089  167535   \n", "1      1.064  ▲  0.007  0.662%  09:30:00  196526   93  156  1.089  167535   \n", "2      1.064  ▲  0.007  0.662%  09:30:01  196526   94  167  1.089  167535   \n", "3      1.063  ▲  0.006  0.568%  09:30:01  248148   94  167  1.084  175645   \n", "4      1.063  ▲  0.006  0.568%  09:30:01  248148   94  171  1.084  175645   \n", "...      ... ..    ...     ...       ...     ...  ...  ...    ...     ...   \n", "15999  1.067  ▲  0.010  0.946%  14:59:58  518999   93  147  1.083  275299   \n", "16000  1.066  ▲  0.009  0.851%  14:59:58  517154   93  147  1.083  273225   \n", "16001  1.066  ▲  0.009  0.851%  14:59:59  517154   93  147  1.083  273225   \n", "16002  1.066  ▲  0.009  0.851%  14:59:59  517104   93  147  1.083  273125   \n", "16003  1.067  ▲  0.010  0.946%  14:59:59  517104   93  147  1.083  273125   \n", "\n", "       ...  买247笔  买248价  买248量  买248笔  买249价  买249量  买249笔  买250价  买250量  \\\n", "0      ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "1      ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "2      ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "3      ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "4      ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "...    ...    ...    ...    ...    ...    ...    ...    ...    ...    ...   \n", "15999  ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "16000  ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "16001  ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "16002  ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "16003  ...    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "\n", "       买250笔  \n", "0        NaN  \n", "1        NaN  \n", "2        NaN  \n", "3        NaN  \n", "4        NaN  \n", "...      ...  \n", "15999    NaN  \n", "16000    NaN  \n", "16001    NaN  \n", "16002    NaN  \n", "16003    NaN  \n", "\n", "[16004 rows x 1513 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取数据\n", "file = os.path.join(os.path.join(root, date), \"千档盘口.csv\")\n", "data = pd.read_csv(file, encoding=\"gbk\")\n", "data"]}, {"cell_type": "code", "execution_count": 6, "id": "b63de00e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>涨跌</th>\n", "      <th>涨跌价</th>\n", "      <th>涨跌幅</th>\n", "      <th>时间</th>\n", "      <th>总卖</th>\n", "      <th>卖档数</th>\n", "      <th>卖均量</th>\n", "      <th>卖均价</th>\n", "      <th>总买</th>\n", "      <th>...</th>\n", "      <th>买7量</th>\n", "      <th>买7笔</th>\n", "      <th>买8价</th>\n", "      <th>买8量</th>\n", "      <th>买8笔</th>\n", "      <th>买9价</th>\n", "      <th>买9量</th>\n", "      <th>买9笔</th>\n", "      <th>买10价</th>\n", "      <th>买10量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:25:00</td>\n", "      <td>196526</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>476</td>\n", "      <td>6</td>\n", "      <td>1.057</td>\n", "      <td>6582</td>\n", "      <td>38</td>\n", "      <td>1.056</td>\n", "      <td>567</td>\n", "      <td>14</td>\n", "      <td>1.055</td>\n", "      <td>2896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:30:00</td>\n", "      <td>196526</td>\n", "      <td>93</td>\n", "      <td>156</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>493</td>\n", "      <td>9</td>\n", "      <td>1.057</td>\n", "      <td>6599</td>\n", "      <td>41</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>18</td>\n", "      <td>1.055</td>\n", "      <td>2881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.064</td>\n", "      <td>▲</td>\n", "      <td>0.007</td>\n", "      <td>0.662%</td>\n", "      <td>09:30:01</td>\n", "      <td>196526</td>\n", "      <td>94</td>\n", "      <td>167</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>...</td>\n", "      <td>6614</td>\n", "      <td>42</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>18</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>53</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.063</td>\n", "      <td>▲</td>\n", "      <td>0.006</td>\n", "      <td>0.568%</td>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>94</td>\n", "      <td>167</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>...</td>\n", "      <td>6614</td>\n", "      <td>42</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>18</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>53</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.063</td>\n", "      <td>▲</td>\n", "      <td>0.006</td>\n", "      <td>0.568%</td>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>94</td>\n", "      <td>171</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>...</td>\n", "      <td>6566</td>\n", "      <td>42</td>\n", "      <td>1.056</td>\n", "      <td>588</td>\n", "      <td>17</td>\n", "      <td>1.055</td>\n", "      <td>3186</td>\n", "      <td>50</td>\n", "      <td>1.054</td>\n", "      <td>6362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15999</th>\n", "      <td>1.067</td>\n", "      <td>▲</td>\n", "      <td>0.010</td>\n", "      <td>0.946%</td>\n", "      <td>14:59:58</td>\n", "      <td>518999</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>275299</td>\n", "      <td>...</td>\n", "      <td>53547</td>\n", "      <td>253</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>50</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>65</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16000</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:58</td>\n", "      <td>517154</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>...</td>\n", "      <td>53547</td>\n", "      <td>253</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>50</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>65</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16001</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:59</td>\n", "      <td>517154</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>...</td>\n", "      <td>53547</td>\n", "      <td>253</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>50</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>65</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16002</th>\n", "      <td>1.066</td>\n", "      <td>▲</td>\n", "      <td>0.009</td>\n", "      <td>0.851%</td>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>...</td>\n", "      <td>53547</td>\n", "      <td>253</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>50</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>65</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16003</th>\n", "      <td>1.067</td>\n", "      <td>▲</td>\n", "      <td>0.010</td>\n", "      <td>0.946%</td>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>93</td>\n", "      <td>147</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>...</td>\n", "      <td>53547</td>\n", "      <td>253</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>50</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>65</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16004 rows × 72 columns</p>\n", "</div>"], "text/plain": ["          现价 涨跌    涨跌价     涨跌幅        时间      总卖  卖档数  卖均量    卖均价      总买  \\\n", "0      1.064  ▲  0.007  0.662%  09:25:00  196526   93  147  1.089  167535   \n", "1      1.064  ▲  0.007  0.662%  09:30:00  196526   93  156  1.089  167535   \n", "2      1.064  ▲  0.007  0.662%  09:30:01  196526   94  167  1.089  167535   \n", "3      1.063  ▲  0.006  0.568%  09:30:01  248148   94  167  1.084  175645   \n", "4      1.063  ▲  0.006  0.568%  09:30:01  248148   94  171  1.084  175645   \n", "...      ... ..    ...     ...       ...     ...  ...  ...    ...     ...   \n", "15999  1.067  ▲  0.010  0.946%  14:59:58  518999   93  147  1.083  275299   \n", "16000  1.066  ▲  0.009  0.851%  14:59:58  517154   93  147  1.083  273225   \n", "16001  1.066  ▲  0.009  0.851%  14:59:59  517154   93  147  1.083  273225   \n", "16002  1.066  ▲  0.009  0.851%  14:59:59  517104   93  147  1.083  273125   \n", "16003  1.067  ▲  0.010  0.946%  14:59:59  517104   93  147  1.083  273125   \n", "\n", "       ...    买7量  买7笔    买8价   买8量  买8笔    买9价   买9量  买9笔   买10价  买10量  \n", "0      ...    476    6  1.057  6582   38  1.056   567   14  1.055  2896  \n", "1      ...    493    9  1.057  6599   41  1.056   605   18  1.055  2881  \n", "2      ...   6614   42  1.056   605   18  1.055  2869   53  1.054  6180  \n", "3      ...   6614   42  1.056   605   18  1.055  2869   53  1.054  6180  \n", "4      ...   6566   42  1.056   588   17  1.055  3186   50  1.054  6362  \n", "...    ...    ...  ...    ...   ...  ...    ...   ...  ...    ...   ...  \n", "15999  ...  53547  253  1.059  2887   50  1.058  5114   65  1.057  7846  \n", "16000  ...  53547  253  1.059  2887   50  1.058  5114   65  1.057  7846  \n", "16001  ...  53547  253  1.059  2887   50  1.058  5114   65  1.057  7846  \n", "16002  ...  53547  253  1.059  2887   50  1.058  5114   65  1.057  7846  \n", "16003  ...  53547  253  1.059  2887   50  1.058  5114   65  1.057  7846  \n", "\n", "[16004 rows x 72 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 限制到十档\n", "data = pd.concat([data.iloc[:, :43], data.iloc[:, 763:792]], axis=1)\n", "data"]}, {"cell_type": "code", "execution_count": 7, "id": "2182695e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>总卖</th>\n", "      <th>卖均价</th>\n", "      <th>总买</th>\n", "      <th>买均价</th>\n", "      <th>卖1价</th>\n", "      <th>卖1量</th>\n", "      <th>卖2价</th>\n", "      <th>卖2量</th>\n", "      <th>卖3价</th>\n", "      <th>...</th>\n", "      <th>买6价</th>\n", "      <th>买6量</th>\n", "      <th>买7价</th>\n", "      <th>买7量</th>\n", "      <th>买8价</th>\n", "      <th>买8量</th>\n", "      <th>买9价</th>\n", "      <th>买9量</th>\n", "      <th>买10价</th>\n", "      <th>买10量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>09:25:00</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3407</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>4736</td>\n", "      <td>1.058</td>\n", "      <td>476</td>\n", "      <td>1.057</td>\n", "      <td>6582</td>\n", "      <td>1.056</td>\n", "      <td>567</td>\n", "      <td>1.055</td>\n", "      <td>2896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>09:30:00</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3377</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>9748</td>\n", "      <td>1.058</td>\n", "      <td>493</td>\n", "      <td>1.057</td>\n", "      <td>6599</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>09:30:01</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>9668</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5497</td>\n", "      <td>1.057</td>\n", "      <td>6566</td>\n", "      <td>1.056</td>\n", "      <td>588</td>\n", "      <td>1.055</td>\n", "      <td>3186</td>\n", "      <td>1.054</td>\n", "      <td>6362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15999</th>\n", "      <td>14:59:58</td>\n", "      <td>518999</td>\n", "      <td>1.083</td>\n", "      <td>275299</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16000</th>\n", "      <td>14:59:58</td>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16001</th>\n", "      <td>14:59:59</td>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16002</th>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16003</th>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16004 rows × 45 columns</p>\n", "</div>"], "text/plain": ["             时间      总卖    卖均价      总买    买均价    卖1价    卖1量    卖2价    卖2量  \\\n", "0      09:25:00  196526  1.089  167535  1.045  1.064   3407  1.063   8764   \n", "1      09:30:00  196526  1.089  167535  1.045  1.064   3377  1.063   8764   \n", "2      09:30:01  196526  1.089  167535  1.045  1.063   8764  1.062  15029   \n", "3      09:30:01  248148  1.084  175645  1.047  1.063   8764  1.062  15029   \n", "4      09:30:01  248148  1.084  175645  1.047  1.063   9668  1.062  15029   \n", "...         ...     ...    ...     ...    ...    ...    ...    ...    ...   \n", "15999  14:59:58  518999  1.083  275299  1.053  1.066  17273  1.065  32215   \n", "16000  14:59:58  517154  1.083  273225  1.053  1.066  17273  1.065  32215   \n", "16001  14:59:59  517154  1.083  273225  1.053  1.066  15556  1.065  32215   \n", "16002  14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "16003  14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "\n", "         卖3价  ...    买6价    买6量    买7价    买7量    买8价   买8量    买9价   买9量  \\\n", "0      1.067  ...  1.059   4736  1.058    476  1.057  6582  1.056   567   \n", "1      1.067  ...  1.059   9748  1.058    493  1.057  6599  1.056   605   \n", "2      1.066  ...  1.058   5488  1.057   6614  1.056   605  1.055  2869   \n", "3      1.066  ...  1.058   5488  1.057   6614  1.056   605  1.055  2869   \n", "4      1.066  ...  1.058   5497  1.057   6566  1.056   588  1.055  3186   \n", "...      ...  ...    ...    ...    ...    ...    ...   ...    ...   ...   \n", "15999  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16000  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16001  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16002  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16003  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "\n", "        买10价  买10量  \n", "0      1.055  2896  \n", "1      1.055  2881  \n", "2      1.054  6180  \n", "3      1.054  6180  \n", "4      1.054  6362  \n", "...      ...   ...  \n", "15999  1.057  7846  \n", "16000  1.057  7846  \n", "16001  1.057  7846  \n", "16002  1.057  7846  \n", "16003  1.057  7846  \n", "\n", "[16004 rows x 45 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 剔除笔数据 及 其他\n", "bads = ['现价','涨跌','涨跌价','涨跌幅','卖档数','卖均量','买档数','买均量']\n", "cols = list(data)\n", "cols = [i for i in cols if '笔' not in i]\n", "cols = [i for i in cols if i not in bads]\n", "data = data.loc[:, cols]\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 8, "id": "a7db7bea", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>卖均</th>\n", "      <th>卖均价</th>\n", "      <th>买均</th>\n", "      <th>买均价</th>\n", "      <th>卖1价</th>\n", "      <th>卖1量</th>\n", "      <th>卖2价</th>\n", "      <th>卖2量</th>\n", "      <th>卖3价</th>\n", "      <th>...</th>\n", "      <th>买6价</th>\n", "      <th>买6量</th>\n", "      <th>买7价</th>\n", "      <th>买7量</th>\n", "      <th>买8价</th>\n", "      <th>买8量</th>\n", "      <th>买9价</th>\n", "      <th>买9量</th>\n", "      <th>买10价</th>\n", "      <th>买10量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>09:25:00</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3407</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>4736</td>\n", "      <td>1.058</td>\n", "      <td>476</td>\n", "      <td>1.057</td>\n", "      <td>6582</td>\n", "      <td>1.056</td>\n", "      <td>567</td>\n", "      <td>1.055</td>\n", "      <td>2896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>09:30:00</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3377</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>9748</td>\n", "      <td>1.058</td>\n", "      <td>493</td>\n", "      <td>1.057</td>\n", "      <td>6599</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>09:30:01</td>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>09:30:01</td>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>9668</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5497</td>\n", "      <td>1.057</td>\n", "      <td>6566</td>\n", "      <td>1.056</td>\n", "      <td>588</td>\n", "      <td>1.055</td>\n", "      <td>3186</td>\n", "      <td>1.054</td>\n", "      <td>6362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15999</th>\n", "      <td>14:59:58</td>\n", "      <td>518999</td>\n", "      <td>1.083</td>\n", "      <td>275299</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16000</th>\n", "      <td>14:59:58</td>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16001</th>\n", "      <td>14:59:59</td>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16002</th>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16003</th>\n", "      <td>14:59:59</td>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16004 rows × 45 columns</p>\n", "</div>"], "text/plain": ["             时间      卖均    卖均价      买均    买均价    卖1价    卖1量    卖2价    卖2量  \\\n", "0      09:25:00  196526  1.089  167535  1.045  1.064   3407  1.063   8764   \n", "1      09:30:00  196526  1.089  167535  1.045  1.064   3377  1.063   8764   \n", "2      09:30:01  196526  1.089  167535  1.045  1.063   8764  1.062  15029   \n", "3      09:30:01  248148  1.084  175645  1.047  1.063   8764  1.062  15029   \n", "4      09:30:01  248148  1.084  175645  1.047  1.063   9668  1.062  15029   \n", "...         ...     ...    ...     ...    ...    ...    ...    ...    ...   \n", "15999  14:59:58  518999  1.083  275299  1.053  1.066  17273  1.065  32215   \n", "16000  14:59:58  517154  1.083  273225  1.053  1.066  17273  1.065  32215   \n", "16001  14:59:59  517154  1.083  273225  1.053  1.066  15556  1.065  32215   \n", "16002  14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "16003  14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "\n", "         卖3价  ...    买6价    买6量    买7价    买7量    买8价   买8量    买9价   买9量  \\\n", "0      1.067  ...  1.059   4736  1.058    476  1.057  6582  1.056   567   \n", "1      1.067  ...  1.059   9748  1.058    493  1.057  6599  1.056   605   \n", "2      1.066  ...  1.058   5488  1.057   6614  1.056   605  1.055  2869   \n", "3      1.066  ...  1.058   5488  1.057   6614  1.056   605  1.055  2869   \n", "4      1.066  ...  1.058   5497  1.057   6566  1.056   588  1.055  3186   \n", "...      ...  ...    ...    ...    ...    ...    ...   ...    ...   ...   \n", "15999  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16000  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16001  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16002  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "16003  1.069  ...  1.061  11477  1.060  53547  1.059  2887  1.058  5114   \n", "\n", "        买10价  买10量  \n", "0      1.055  2896  \n", "1      1.055  2881  \n", "2      1.054  6180  \n", "3      1.054  6180  \n", "4      1.054  6362  \n", "...      ...   ...  \n", "15999  1.057  7846  \n", "16000  1.057  7846  \n", "16001  1.057  7846  \n", "16002  1.057  7846  \n", "16003  1.057  7846  \n", "\n", "[16004 rows x 45 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#  重命名\n", "cols[1] = '卖均'\n", "cols[3] = '买均'\n", "\n", "data.columns = cols\n", "data"]}, {"cell_type": "code", "execution_count": 9, "id": "23d31e3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2023-09-01'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["date[:4] + '-' + date[4:6] + \"-\" + date[6:]"]}, {"cell_type": "code", "execution_count": 10, "id": "6efd5d5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>卖均</th>\n", "      <th>卖均价</th>\n", "      <th>买均</th>\n", "      <th>买均价</th>\n", "      <th>卖1价</th>\n", "      <th>卖1量</th>\n", "      <th>卖2价</th>\n", "      <th>卖2量</th>\n", "      <th>卖3价</th>\n", "      <th>卖3量</th>\n", "      <th>...</th>\n", "      <th>买6价</th>\n", "      <th>买6量</th>\n", "      <th>买7价</th>\n", "      <th>买7量</th>\n", "      <th>买8价</th>\n", "      <th>买8量</th>\n", "      <th>买9价</th>\n", "      <th>买9量</th>\n", "      <th>买10价</th>\n", "      <th>买10量</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:25:00</th>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3407</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>5951</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>4736</td>\n", "      <td>1.058</td>\n", "      <td>476</td>\n", "      <td>1.057</td>\n", "      <td>6582</td>\n", "      <td>1.056</td>\n", "      <td>567</td>\n", "      <td>1.055</td>\n", "      <td>2896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:00</th>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.064</td>\n", "      <td>3377</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.067</td>\n", "      <td>11253</td>\n", "      <td>...</td>\n", "      <td>1.059</td>\n", "      <td>9748</td>\n", "      <td>1.058</td>\n", "      <td>493</td>\n", "      <td>1.057</td>\n", "      <td>6599</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>196526</td>\n", "      <td>1.089</td>\n", "      <td>167535</td>\n", "      <td>1.045</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>8524</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>8764</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>8524</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5488</td>\n", "      <td>1.057</td>\n", "      <td>6614</td>\n", "      <td>1.056</td>\n", "      <td>605</td>\n", "      <td>1.055</td>\n", "      <td>2869</td>\n", "      <td>1.054</td>\n", "      <td>6180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:01</th>\n", "      <td>248148</td>\n", "      <td>1.084</td>\n", "      <td>175645</td>\n", "      <td>1.047</td>\n", "      <td>1.063</td>\n", "      <td>9668</td>\n", "      <td>1.062</td>\n", "      <td>15029</td>\n", "      <td>1.066</td>\n", "      <td>8702</td>\n", "      <td>...</td>\n", "      <td>1.058</td>\n", "      <td>5497</td>\n", "      <td>1.057</td>\n", "      <td>6566</td>\n", "      <td>1.056</td>\n", "      <td>588</td>\n", "      <td>1.055</td>\n", "      <td>3186</td>\n", "      <td>1.054</td>\n", "      <td>6362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:58</th>\n", "      <td>518999</td>\n", "      <td>1.083</td>\n", "      <td>275299</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>46199</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:58</th>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>17273</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>46199</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>517154</td>\n", "      <td>1.083</td>\n", "      <td>273225</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>46199</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>46199</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>517104</td>\n", "      <td>1.083</td>\n", "      <td>273125</td>\n", "      <td>1.053</td>\n", "      <td>1.066</td>\n", "      <td>15556</td>\n", "      <td>1.065</td>\n", "      <td>32215</td>\n", "      <td>1.069</td>\n", "      <td>46199</td>\n", "      <td>...</td>\n", "      <td>1.061</td>\n", "      <td>11477</td>\n", "      <td>1.060</td>\n", "      <td>53547</td>\n", "      <td>1.059</td>\n", "      <td>2887</td>\n", "      <td>1.058</td>\n", "      <td>5114</td>\n", "      <td>1.057</td>\n", "      <td>7846</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16004 rows × 44 columns</p>\n", "</div>"], "text/plain": ["                         卖均    卖均价      买均    买均价    卖1价    卖1量    卖2价    卖2量  \\\n", "时间                                                                              \n", "2023-09-01 09:25:00  196526  1.089  167535  1.045  1.064   3407  1.063   8764   \n", "2023-09-01 09:30:00  196526  1.089  167535  1.045  1.064   3377  1.063   8764   \n", "2023-09-01 09:30:01  196526  1.089  167535  1.045  1.063   8764  1.062  15029   \n", "2023-09-01 09:30:01  248148  1.084  175645  1.047  1.063   8764  1.062  15029   \n", "2023-09-01 09:30:01  248148  1.084  175645  1.047  1.063   9668  1.062  15029   \n", "...                     ...    ...     ...    ...    ...    ...    ...    ...   \n", "2023-09-01 14:59:58  518999  1.083  275299  1.053  1.066  17273  1.065  32215   \n", "2023-09-01 14:59:58  517154  1.083  273225  1.053  1.066  17273  1.065  32215   \n", "2023-09-01 14:59:59  517154  1.083  273225  1.053  1.066  15556  1.065  32215   \n", "2023-09-01 14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "2023-09-01 14:59:59  517104  1.083  273125  1.053  1.066  15556  1.065  32215   \n", "\n", "                       卖3价    卖3量  ...    买6价    买6量    买7价    买7量    买8价  \\\n", "时间                                 ...                                      \n", "2023-09-01 09:25:00  1.067   5951  ...  1.059   4736  1.058    476  1.057   \n", "2023-09-01 09:30:00  1.067  11253  ...  1.059   9748  1.058    493  1.057   \n", "2023-09-01 09:30:01  1.066   8524  ...  1.058   5488  1.057   6614  1.056   \n", "2023-09-01 09:30:01  1.066   8524  ...  1.058   5488  1.057   6614  1.056   \n", "2023-09-01 09:30:01  1.066   8702  ...  1.058   5497  1.057   6566  1.056   \n", "...                    ...    ...  ...    ...    ...    ...    ...    ...   \n", "2023-09-01 14:59:58  1.069  46199  ...  1.061  11477  1.060  53547  1.059   \n", "2023-09-01 14:59:58  1.069  46199  ...  1.061  11477  1.060  53547  1.059   \n", "2023-09-01 14:59:59  1.069  46199  ...  1.061  11477  1.060  53547  1.059   \n", "2023-09-01 14:59:59  1.069  46199  ...  1.061  11477  1.060  53547  1.059   \n", "2023-09-01 14:59:59  1.069  46199  ...  1.061  11477  1.060  53547  1.059   \n", "\n", "                      买8量    买9价   买9量   买10价  买10量  \n", "时间                                                   \n", "2023-09-01 09:25:00  6582  1.056   567  1.055  2896  \n", "2023-09-01 09:30:00  6599  1.056   605  1.055  2881  \n", "2023-09-01 09:30:01   605  1.055  2869  1.054  6180  \n", "2023-09-01 09:30:01   605  1.055  2869  1.054  6180  \n", "2023-09-01 09:30:01   588  1.055  3186  1.054  6362  \n", "...                   ...    ...   ...    ...   ...  \n", "2023-09-01 14:59:58  2887  1.058  5114  1.057  7846  \n", "2023-09-01 14:59:58  2887  1.058  5114  1.057  7846  \n", "2023-09-01 14:59:59  2887  1.058  5114  1.057  7846  \n", "2023-09-01 14:59:59  2887  1.058  5114  1.057  7846  \n", "2023-09-01 14:59:59  2887  1.058  5114  1.057  7846  \n", "\n", "[16004 rows x 44 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 补充日期, 设置索引\n", "data['时间'] = data['时间'].apply(lambda x: date[:4] + '-' + date[4:6] + \"-\" + date[6:] + \" \" + x)\n", "data['时间'] = pd.to_datetime(data['时间'])\n", "data = data.set_index(\"时间\", drop=True)\n", "data"]}, {"cell_type": "code", "execution_count": 11, "id": "720627fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["['卖均',\n", " '卖均价',\n", " '买均',\n", " '买均价',\n", " '卖1价',\n", " '卖1量',\n", " '卖2价',\n", " '卖2量',\n", " '卖3价',\n", " '卖3量',\n", " '卖4价',\n", " '卖4量',\n", " '卖5价',\n", " '卖5量',\n", " '卖6价',\n", " '卖6量',\n", " '卖7价',\n", " '卖7量',\n", " '卖8价',\n", " '卖8量',\n", " '卖9价',\n", " '卖9量',\n", " '卖10价',\n", " '卖10量',\n", " '买1价',\n", " '买1量',\n", " '买2价',\n", " '买2量',\n", " '买3价',\n", " '买3量',\n", " '买4价',\n", " '买4量',\n", " '买5价',\n", " '买5量',\n", " '买6价',\n", " '买6量',\n", " '买7价',\n", " '买7量',\n", " '买8价',\n", " '买8量',\n", " '买9价',\n", " '买9量',\n", " '买10价',\n", " '买10量']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["list(data)"]}, {"cell_type": "code", "execution_count": 12, "id": "abbb2ff5", "metadata": {}, "outputs": [{"data": {"text/plain": ["卖均        int64\n", "卖均价     float64\n", "买均        int64\n", "买均价     float64\n", "卖1价     float64\n", "卖1量       int64\n", "卖2价     float64\n", "卖2量       int64\n", "卖3价     float64\n", "卖3量       int64\n", "卖4价     float64\n", "卖4量       int64\n", "卖5价     float64\n", "卖5量       int64\n", "卖6价     float64\n", "卖6量       int64\n", "卖7价     float64\n", "卖7量       int64\n", "卖8价     float64\n", "卖8量       int64\n", "卖9价     float64\n", "卖9量       int64\n", "卖10价    float64\n", "卖10量      int64\n", "买1价     float64\n", "买1量     float64\n", "买2价     float64\n", "买2量     float64\n", "买3价     float64\n", "买3量       int64\n", "买4价     float64\n", "买4量       int64\n", "买5价     float64\n", "买5量       int64\n", "买6价     float64\n", "买6量       int64\n", "买7价     float64\n", "买7量       int64\n", "买8价     float64\n", "买8量       int64\n", "买9价     float64\n", "买9量       int64\n", "买10价    float64\n", "买10量      int64\n", "dtype: object"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1c4222e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}