import pandas as pd
import numpy as np
import os

from ...base.base_data import sdpk_base
from ...config import get_his_dates
from ...tool import std_3s_time_data, get_his_raw_data, drop_duplicate_row


class sdpk_featrue(sdpk_base):
    def name(self):
        """
        返回特征名称
        """
        return "十档盘口"


class sdpk_001(sdpk_featrue):
    """
    1 + 64

    '时间' + 
    ['卖10价', '卖10量', '卖9价', '卖9量', '卖8价', '卖8量', '卖7价', '卖7量', '卖6价', '卖6量', '卖5价', '卖5量', '卖4价', '卖4量', '卖3价', '卖3量', '卖2价', '卖2量', '卖1价', '卖1量', '买1价', '买1量', '买2价', '买2量', '买3价', '买3量', '买4价', '买4量', '买5价', '买5量', '买6价', '买6量', '买7价', '买7量', '买8价', '买8量', '买9价', '买9量', '买10价', '买10量', '卖均', '总卖', '买均', '总买', '卖1OF', '买1OF', '卖2OF', '买2OF', '卖3OF', '买3OF', '卖4OF', '买4OF', '卖5OF', '买5OF', '卖6OF', '买6OF', '卖7OF', '买7OF', '卖8OF', '买8OF', '卖9OF', '买9OF', '卖10OF', '买10OF']
    """

    def feature_check(self, data: pd.DataFrame):
        return True

    def version(self):
        return "001_20230911"

    def need_raw_cols(self):
        return [
            '时间',
            '卖10价',
            '卖10量',
            '卖9价',
            '卖9量',
            '卖8价',
            '卖8量',
            '卖7价',
            '卖7量',
            '卖6价',
            '卖6量',
            '卖5价',
            '卖5量',
            '卖4价',
            '卖4量',
            '卖3价',
            '卖3量',
            '卖2价',
            '卖2量',
            '卖1价',
            '卖1量',
            '买1价',
            '买1量',
            '买2价',
            '买2量',
            '买3价',
            '买3量',
            '买4价',
            '买4量',
            '买5价',
            '买5量',
            '买6价',
            '买6量',
            '买7价',
            '买7量',
            '买8价',
            '买8量',
            '买9价',
            '买9量',
            '买10价',
            '买10量',
            '卖均',
            '总卖',
            '买均',
            '总买'
        ]

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 特征补充
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                coef = -1 if side == 0 else 1

                cond = (data[f'{side_name}{idx}价'] - data[f'{side_name}{idx}价'].shift()
                        ).apply(lambda x: 1 if x > 0 else -1 if x < 0 else np.nan)
                v1 = data[f'{side_name}{idx}量'] * cond * coef
                v2 = data[f'{side_name}{idx}量'] - \
                    data[f'{side_name}{idx}量'].shift()
                data[f'{side_name}{idx}OF'] = v1.fillna(v2)

        data.fillna(0, inplace=True)
        return data


class sdpk_002(sdpk_featrue):
    """
    复现研报特征:
    20131211-民生证券-CTA程序化交易实务研究之六：基于机器学习的订单簿高频交易策略

        1. 买一价 
        2. 卖一价
        3. 买一量
        4. 卖一量
        5. 买一对数收益率  
            相邻两个买一价格的对数差
        6. 卖一对数收益率:  
            相邻两个卖一价格的对数差
        7. 相对价差  
            价差/((买一价+卖一价)/2)
        8. 买一量对数差  
            和上一个买一量的对数差
        9. 卖一量对数差  
            和上一个卖一量的对数差
        10. 斜率  
            价差/深度
        11. 深度  
            (买一量+卖一量 )/2

    # 以下特征不在此计算
        14. 最新价  
            最新的撮合成交价
        15. 成交量  
            当天累计的成交量
        16. 成交量对数差

    # 以下特征不计算
        12. 持仓量
        13. 持仓量对数差
        17. 基差
    """

    def feature_check(self, data: pd.DataFrame):
        return True

    def version(self):
        return "002_20231027"

    def need_raw_cols(self):
        return [
            '时间',
            '卖1价',
            '卖1量',
            '买1价',
            '买1量',
        ]

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作

        """
        # 5. 买一对数收益率
        # 相邻两个买一价格的对数差
        data['b1_log_ret'] = np.log(data['买1价']/data['买1价'].shift())

        # 6. 卖一对数收益率:
        # 相邻两个卖一价格的对数差
        data['s1_log_ret'] = np.log(data['卖1价']/data['卖1价'].shift())

        # 7. 相对价差
        # 价差/((买一价+卖一价)/2)
        spread = data['卖1价'] - data['买1价']  # 价差
        data['rel_spread'] = spread / \
            ((data['买1价'] + data['卖1价'])/2)

        # 8. 买一量对数差
        # 和上一个买一量的对数差
        data['b1_vol_log_ret'] = np.log(data['买1量']/data['买1量'].shift())

        # 9. 卖一量对数差
        # 和上一个卖一量的对数差
        data['s1_vol_log_ret'] = np.log(data['卖1量']/data['卖1量'].shift())

        # 11. 深度
        # (买一量+卖一量 )/2
        data['depth'] = (data['买1量'] + data['卖1量'])/2

        # 10. 斜率
        # 价差/深度
        data['slope'] = spread / data['depth']

        return data.loc[:, [
            'b1_log_ret',
            's1_log_ret',
            'rel_spread',
            'b1_vol_log_ret',
            's1_vol_log_ret',
            'depth',
            'slope',
        ]].fillna(0)


class sdpk_003(sdpk_featrue):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档买价，第一档买量  
    第一档卖价，第一档卖量  
    第二档买价，第二档买量  
    第二档卖价，第二档卖量  
    。。。  
    第十档买价，第十档买量  
    第十档卖价，第十档卖量

    # 数据处理
    标准化  
    (data - data.mean())/data.std()

    其中 mean 和 std 分别是前一天数据的均值和标准差。

    """

    def feature_check(self, data: pd.DataFrame):
        return True

    def version(self):
        return "003_20231028"

    def need_raw_cols(self):
        return [
            '卖10价',
            '卖10量',
            '卖9价',
            '卖9量',
            '卖8价',
            '卖8量',
            '卖7价',
            '卖7量',
            '卖6价',
            '卖6量',
            '卖5价',
            '卖5量',
            '卖4价',
            '卖4量',
            '卖3价',
            '卖3量',
            '卖2价',
            '卖2量',
            '卖1价',
            '卖1量',
            '买1价',
            '买1量',
            '买2价',
            '买2量',
            '买3价',
            '买3量',
            '买4价',
            '买4量',
            '买5价',
            '买5量',
            '买6价',
            '买6量',
            '买7价',
            '买7量',
            '买8价',
            '买8量',
            '买9价',
            '买9量',
            '买10价',
            '买10量',
        ]

    def _get_price_vol_cols(self):
        """
        返回 价格和量的列名
        """
        price_cols = [
            '卖10价',
            '卖9价',
            '卖8价',
            '卖7价',
            '卖6价',
            '卖5价',
            '卖4价',
            '卖3价',
            '卖2价',
            '卖1价',
            '买1价',
            '买2价',
            '买3价',
            '买4价',
            '买5价',
            '买6价',
            '买7价',
            '买8价',
            '买9价',
            '买10价',
        ]
        vol_cols = [i.replace('价', '量') for i in price_cols]

        return price_cols, vol_cols

    def _get_pre_date_price_vol_mean_std(self, pre_trade_date, code):
        """
        获取前一天的价格和量的均值和标准差

        (price_mean, price_std, vol_mean, vol_std)
        """
        # 读取数据
        file = f"D:/通达信录制数据/his_data/{pre_trade_date.replace('-', '')}/{code}/十档盘口.csv"
        if not os.path.exists(file):
            return None, None, None, None

        data = pd.read_csv(file, encoding='gbk')
        data['时间'] = pd.to_datetime(data['时间'])
        data.set_index('时间', inplace=True, drop=True)
        data = std_3s_time_data(data)

        # 替换所有的0值为nan
        data.replace(0, np.nan, inplace=True)

        price_cols, vol_cols = self._get_price_vol_cols()
        price_data = data.loc[:, price_cols]
        vol_data = data.loc[:, vol_cols]

        # 计算均值和标准差
        price_mean = np.mean(price_data.values)
        price_std = np.std(price_data.values)
        vol_mean = np.mean(vol_data.values)
        vol_std = np.std(vol_data.values)

        return price_mean, price_std, vol_mean, vol_std

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一天的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx == 0:
            return
        pre_trade_date = dates[cur_idx-1]

        # 获取前一天的 均值和标准差
        price_mean, price_std, vol_mean, vol_std = self._get_pre_date_price_vol_mean_std(
            pre_trade_date, self.code)
        if None is price_mean:
            return None

        # 标准化数据
        price_cols, vol_cols = self._get_price_vol_cols()

        # 替换0值为nan
        data.replace(0, np.nan, inplace=True)

        data.loc[:, price_cols] = (
            data.loc[:, price_cols] - price_mean) / price_std
        data.loc[:, vol_cols] = (data.loc[:, vol_cols] - vol_mean) / vol_std

        # 替换nan为0
        data.fillna(0, inplace=True)

        # 输出列顺序
        out_cols = []
        for p, v in zip(price_cols, vol_cols):
            out_cols.append(p)
            out_cols.append(v)

        return data.loc[:, out_cols]


class sdpk_004(sdpk_003):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    # 数据处理
    标准化  
    (data - data.mean())/data.std()

    其中 mean 和 std 分别是前一天数据的均值和标准差。

    与 003 的区别，调整输出顺序与论文一致
    """

    def version(self):
        return "004_20231030"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一天的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx == 0:
            return
        pre_trade_date = dates[cur_idx-1]

        # 获取前一天的 均值和标准差
        price_mean, price_std, vol_mean, vol_std = self._get_pre_date_price_vol_mean_std(
            pre_trade_date, self.code)
        if None is price_mean:
            return None

        # 标准化数据
        price_cols, vol_cols = self._get_price_vol_cols()

        # 替换0值为nan
        data.replace(0, np.nan, inplace=True)

        data.loc[:, price_cols] = (
            data.loc[:, price_cols] - price_mean) / price_std
        data.loc[:, vol_cols] = (data.loc[:, vol_cols] - vol_mean) / vol_std

        # 替换nan为0
        data.fillna(0, inplace=True)

        ##############################
        # 004 变更
        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')
        ##############################

        return data.loc[:, out_cols]


class sdpk_005(sdpk_003):
    """
    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    不进行标准化处理
    """

    def version(self):
        return "005_20231101"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 004 变更
        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_006(sdpk_004):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    # 数据处理
    标准化  
    (data - data.mean())/data.std()

    其中 mean 和 std 分别是前一天数据的均值和标准差。

    与 004 的区别: 
    1. 剔除数据中0值的行
    2. 按照每列数据进行标准化
    """

    def version(self):
        return "006_20231102"

    def _get_pre_date(self, pre_trade_date, code):
        """
        获取前一天的盘口数据
        dataframe
        """
        # 读取数据
        file = f"D:/通达信录制数据/his_data/{pre_trade_date.replace('-', '')}/{code}/十档盘口.csv"
        if not os.path.exists(file):
            return None

        data = pd.read_csv(file, encoding='gbk')
        data['时间'] = pd.to_datetime(data['时间'])
        data.set_index('时间', inplace=True, drop=True)
        data = std_3s_time_data(data)

        # 替换所有的0值为nan
        data.replace(0, np.nan, inplace=True)
        self.data = data

    def _get_mean_std(self, col_name):
        """按照列获取均值和标准差"""
        return self.data[col_name].mean(), self.data[col_name].std()

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一天的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx == 0:
            return
        pre_trade_date = dates[cur_idx-1]

        # 替换0值为nan
        data.replace(0, np.nan, inplace=True)

        # 获取前一天的盘口数据
        self._get_pre_date(pre_trade_date, self.code)

        # 按列标准化数据
        for col in list(data):
            _mean, _std = self._get_mean_std(col)
            data[col] = (data[col] - _mean) / _std

        # 替换nan为0
        data.fillna(0, inplace=True)

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_007(sdpk_001):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    # 数据处理
    标准化  
    (data - data.mean())/data.std()

    与 006 的区别: 
        其中 mean 和 std 分别是前5天数据的均值和标准差。
    """

    def version(self):
        return "007_20240105"

    def _get_pre_data(self, pre_trade_date, code):
        """
        获取之前的盘口数据
        并格式化
        """
        # 读取数据
        data = get_his_raw_data(code, pre_trade_date, self.raw_name())
        if None is data:
            return None

        data = self.std_format(data)

        # data.replace(0, np.nan, inplace=True)
        return data

    def _get_mean_std(self, pre_datas, col_name):
        """按照列获取均值和标准差"""
        return pre_datas[col_name].mean(), pre_datas[col_name].std()

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前5天的交易日期 2023-01-01
        # 0， 1 ， 3， 4， 5， 6， 7
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 6:
            return
        pre_trade_dates = dates[cur_idx-5:cur_idx]

        # # 替换0值为nan
        # data.replace(0, np.nan, inplace=True)

        # 获取前5天的盘口数据
        pre_datas = pd.DataFrame()
        for date in pre_trade_dates:
            _data = self._get_pre_data(date, self.code)
            if None is _data:
                return None
            pre_datas = pd.concat([pre_datas, _data], axis=0)

        # 按列标准化数据
        for col in list(data):
            _mean, _std = self._get_mean_std(pre_datas, col)
            data[col] = (data[col] - _mean) / _std

        # # 替换nan为0
        # data.fillna(0, inplace=True)

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_008(sdpk_007):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    与 006 的区别: 
        标准化只区分价格与量，不每一个挡位价格数量单独标准化
    """

    def version(self):
        return "008_20240109"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前5天的交易日期 2023-01-01
        # 0， 1 ， 3， 4， 5， 6， 7
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 6:
            return
        pre_trade_dates = dates[cur_idx-5:cur_idx]

        # # 替换0值为nan
        # data.replace(0, np.nan, inplace=True)

        # 获取前5天的盘口数据
        pre_datas = pd.DataFrame()
        for date in pre_trade_dates:
            _data = self._get_pre_data(date, self.code)
            if None is _data:
                return None
            pre_datas = pd.concat([pre_datas, _data], axis=0)

        # 按照价格和量标准化数据
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]
        for cols in [price_cols, vol_cols]:
            _mean, _std = pre_datas.loc[:, cols].mean(
            ), pre_datas.loc[:, cols].std()
            data.loc[:, cols] = (data.loc[:, cols] - _mean) / _std

        # # 替换nan为0
        # data.fillna(0, inplace=True)

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_009(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    与 006 的区别: 
        标准化只区分价格与量，不每一个挡位价格数量单独标准化
    """

    def version(self):
        return "009_20240115"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前5天的交易日期 2023-01-01
        # 0， 1 ， 3， 4， 5， 6， 7
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 6:
            return
        pre_trade_dates = dates[cur_idx-5:cur_idx]

        # 获取前5天的盘口数据
        pre_datas = pd.DataFrame()
        for date in pre_trade_dates:
            _data = self._get_pre_data(date, self.code)
            if None is _data:
                return None
            pre_datas = pd.concat([pre_datas, _data], axis=0)

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 按照价格和量标准化数据
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]
        for cols in [price_cols, vol_cols]:
            _mean, _std = pre_datas.loc[:, cols].mean(
            ), pre_datas.loc[:, cols].std()
            data.loc[:, cols] = (data.loc[:, cols] - _mean) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_010(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量
    """

    def version(self):
        return "010_20240116"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前5天的交易日期 2023-01-01
        # 0， 1 ， 3， 4， 5， 6， 7
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 6:
            return
        pre_trade_dates = dates[cur_idx-5:cur_idx]

        # 获取前5天的盘口数据
        pre_datas = pd.DataFrame()
        for date in pre_trade_dates:
            _data = self._get_pre_data(date, self.code)
            if None is _data:
                return None
            pre_datas = pd.concat([pre_datas, _data], axis=0)

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 按照价格和量标准化数据
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]
        for cols in [price_cols, vol_cols]:
            _mean, _std = np.mean(pre_datas.loc[:, cols].values), np.std(
                pre_datas.loc[:, cols].values)
            data.loc[:, cols] = (data.loc[:, cols] - _mean) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_011(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    保存 标准化数据
    """

    def version(self):
        return "011_20240116"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前5天的交易日期 2023-01-01
        # 0， 1 ， 3， 4， 5， 6， 7
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 6:
            return
        pre_trade_dates = dates[cur_idx-5:cur_idx]

        # 获取前5天的盘口数据
        pre_datas = pd.DataFrame()
        for date in pre_trade_dates:
            _data = self._get_pre_data(date, self.code)
            if None is _data:
                return None
            pre_datas = pd.concat([pre_datas, _data], axis=0)

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 按照价格和量标准化数据
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]
        names = ['price', 'vol']
        for i, cols in enumerate([price_cols, vol_cols]):
            _mean, _std = np.mean(pre_datas.loc[:, cols].values), np.std(
                pre_datas.loc[:, cols].values)
            data.loc[:, cols] = (data.loc[:, cols] - _mean) / _std
            data[names[i]+'_mean'] = _mean
            data[names[i]+'_std'] = _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        out_cols.append('price_mean')
        out_cols.append('price_std')
        out_cols.append('vol_mean')
        out_cols.append('vol_std')

        return data.loc[:, out_cols]


class sdpk_012(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    价格标准化
        价格 / 最近时间中间价格 - 1

    数量标准化 
        根据前一个交易日的数据进行标准化

    """

    def version(self):
        return "012_20240119"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一个的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 1:
            return
        pre_trade_date = dates[cur_idx-1]

        # 获取前1天的盘口数据
        pre_datas = self._get_pre_data(pre_trade_date, self.code)
        if None is pre_datas:
            return None

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 区分价量列
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]

        # 价格标准化
        _latest_data = data.iloc[-1]
        min_price = (_latest_data['买1价'] + _latest_data['卖1价']) / 2
        data.loc[:, price_cols] = data.loc[:, price_cols] / min_price - 1

        # 量标准化
        _meam, _std = np.mean(pre_datas.loc[:, vol_cols].values), np.std(
            pre_datas.loc[:, vol_cols].values)
        data.loc[:, vol_cols] = (data.loc[:, vol_cols] - _meam) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_013(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    价格标准化
        1. 价格 / 最近时间中间价格 - 1
        2. 根据前一个交易日的数据进行标准化

    数量标准化 
        根据前一个交易日的数据进行标准化

    """

    def version(self):
        return "013_20240119"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一个的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 1:
            return
        pre_trade_date = dates[cur_idx-1]

        # 获取前1天的盘口数据
        pre_datas = self._get_pre_data(pre_trade_date, self.code)
        if None is pre_datas:
            return None

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 区分价量列
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]

        # 价格标准化
        for _data in [pre_datas, data]:
            _latest_data = _data.iloc[-1]
            min_price = (_latest_data['买1价'] + _latest_data['卖1价']) / 2
            _data.loc[:, price_cols] = _data.loc[:, price_cols] / min_price - 1

        # 量标准化
        for cols in [vol_cols, price_cols]:
            _meam, _std = np.mean(pre_datas.loc[:, cols].values), np.std(
                pre_datas.loc[:, cols].values)
            data.loc[:, cols] = (data.loc[:, cols] - _meam) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_014(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    价格标准化
        (价格 / 最近时间中间价格 - 1) * 10 

    数量标准化 
        根据前一个交易日的数据进行标准化

    """

    def version(self):
        return "014_20240119"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 获取前一个的交易日期 2023-01-01
        dates = get_his_dates()
        cur_idx = dates.index(self.date)
        if cur_idx < 1:
            return
        pre_trade_date = dates[cur_idx-1]

        # 获取前1天的盘口数据
        pre_datas = self._get_pre_data(pre_trade_date, self.code)
        if None is pre_datas:
            return None

        # 删除前5天重复数据
        pre_datas = drop_duplicate_row(pre_datas)

        # 删除重复数据
        data = drop_duplicate_row(data)

        # 区分价量列
        price_cols = [f'卖{i+1}价' for i in range(10)]
        price_cols += [f'买{i+1}价' for i in range(10)]
        vol_cols = [f'卖{i+1}量' for i in range(10)]
        vol_cols += [f'买{i+1}量' for i in range(10)]

        # 价格标准化
        _latest_data = data.iloc[-1]
        min_price = (_latest_data['买1价'] + _latest_data['卖1价']) / 2
        data.loc[:, price_cols] = (
            data.loc[:, price_cols] / min_price - 1) * 10

        # 量标准化
        _meam, _std = np.mean(pre_datas.loc[:, vol_cols].values), np.std(
            pre_datas.loc[:, vol_cols].values)
        data.loc[:, vol_cols] = (data.loc[:, vol_cols] - _meam) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_015(sdpk_008):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    单个样本内做标准化
    此处不做标准化
        价格标准化
            temp = 价格 / 最近时间中间价格 - 1
            价格 = temp / temp(std)

        数量标准化 
            当前样本内的数据进行标准化

    """

    def version(self):
        return "015_20240119"

    # def get(self, code, date, his: bool):
    #     return super().get(code, date, his, False)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 删除重复数据
        data = drop_duplicate_row(data)

        # # 价格标准化
        # _latest_data = data.iloc[-1]
        # min_price = (_latest_data['买1价'] + _latest_data['卖1价']) / 2
        # data.loc[:, price_cols] = (
        #     data.loc[:, price_cols] / min_price - 1) * 10
        # data.loc[:, price_cols] = data.loc[:, price_cols] / \
        #     np.std(data.loc[:, price_cols].values)

        # # 量标准化
        # _meam, _std = np.mean(data.loc[:, vol_cols].values), np.std(
        #     data.loc[:, vol_cols].values)
        # data.loc[:, vol_cols] = (data.loc[:, vol_cols] - _meam) / _std

        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_without_std(sdpk_009):
    """
    # 复现论文特征:
    DeepLOB Deep Convolutional Neural Networks

    # 特征
    第一档卖价，第一档卖量，第一档买价，第一档买量
    第二档卖价，第二档卖量，第二档买价，第二档买量
    。。。  
    第十档卖价，第十档卖量，第十档买价，第十档买量

    与 006 的区别: 
        标准化只区分价格与量，不每一个挡位价格数量单独标准化
    """

    def version(self):
        return "sdpk_without_std_20240115"

    def get(self, code, date, his: bool):
        return super().get(code, date, his, False)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 输出列顺序
        out_cols = []
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                out_cols.append(f'{side_name}{idx}价')
                out_cols.append(f'{side_name}{idx}量')

        return data.loc[:, out_cols]


class sdpk_016(sdpk_001):
    """
    机器学习 特征
    """

    def version(self):
        return "016_20240119"

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        # 删除重复数据
        data = drop_duplicate_row(data)

        # 特征补充
        for i in range(10):
            idx = i+1
            for side in range(2):
                side_name = "卖" if side == 0 else "买"
                coef = -1 if side == 0 else 1

                cond = (data[f'{side_name}{idx}价'] - data[f'{side_name}{idx}价'].shift()
                        ).apply(lambda x: 1 if x > 0 else -1 if x < 0 else np.nan)
                v1 = data[f'{side_name}{idx}量'] * cond * coef
                v2 = data[f'{side_name}{idx}量'] - \
                    data[f'{side_name}{idx}量'].shift()
                data[f'{side_name}{idx}OF'] = v1.fillna(v2)

        if data.iloc[1:].isna().sum().sum() > 0:
            raise Exception("存在空值")

        return data
