{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f80b2091", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import shutil\n", "\n", "from config import get_realtime_data_path, get_his_data_path"]}, {"cell_type": "code", "execution_count": 2, "id": "e605079b", "metadata": {}, "outputs": [], "source": ["# 设置项\n", "date = '20230901'"]}, {"cell_type": "code", "execution_count": 3, "id": "4dd5637d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>...</th>\n", "      <th>昨PV</th>\n", "      <th>PV涨</th>\n", "      <th>换手</th>\n", "      <th>溢价</th>\n", "      <th>溢率</th>\n", "      <th>资产</th>\n", "      <th>净资</th>\n", "      <th>流通</th>\n", "      <th>时点净值</th>\n", "      <th>昨日单位净值</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:05:03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:05:08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:05:18</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:05:23</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:05:33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3462</th>\n", "      <td>2023-09-01 14:59:54</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>...</td>\n", "      <td>1.0300</td>\n", "      <td>-0.34%</td>\n", "      <td>0.96%</td>\n", "      <td>0.0405</td>\n", "      <td>3.95%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3463</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>...</td>\n", "      <td>1.0300</td>\n", "      <td>-0.34%</td>\n", "      <td>0.96%</td>\n", "      <td>0.0405</td>\n", "      <td>3.95%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3464</th>\n", "      <td>2023-09-01 14:59:59</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>...</td>\n", "      <td>1.0300</td>\n", "      <td>-0.34%</td>\n", "      <td>0.96%</td>\n", "      <td>0.0395</td>\n", "      <td>3.85%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3465</th>\n", "      <td>2023-09-01 15:00:03</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>...</td>\n", "      <td>1.0300</td>\n", "      <td>-0.34%</td>\n", "      <td>0.96%</td>\n", "      <td>0.0395</td>\n", "      <td>3.85%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3466</th>\n", "      <td>2023-09-01 15:00:06</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>...</td>\n", "      <td>1.0300</td>\n", "      <td>-0.34%</td>\n", "      <td>0.96%</td>\n", "      <td>0.0405</td>\n", "      <td>3.95%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3467 rows × 32 columns</p>\n", "</div>"], "text/plain": ["                       时间     现价     今开     涨跌     最高     涨幅     最低     昨收  \\\n", "0     2023-09-01 09:05:03    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "1     2023-09-01 09:05:08    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "2     2023-09-01 09:05:18    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "3     2023-09-01 09:05:23    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "4     2023-09-01 09:05:33    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "...                   ...    ...    ...    ...    ...    ...    ...    ...   \n", "3462  2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "3463  2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "3464  2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85%  1.060  1.057   \n", "3465  2023-09-01 15:00:03  1.066  1.064  0.009  1.069  0.85%  1.060  1.057   \n", "3466  2023-09-01 15:00:06  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "\n", "         均价     振幅  ...     昨PV     PV涨     换手      溢价     溢率   资产   净资   流通  \\\n", "0       NaN    NaN  ...     NaN     NaN    NaN     NaN    NaN  NaN  NaN  NaN   \n", "1       NaN    NaN  ...     NaN     NaN    NaN     NaN    NaN  NaN  NaN  NaN   \n", "2       NaN    NaN  ...     NaN     NaN    NaN     NaN    NaN  NaN  NaN  NaN   \n", "3       NaN    NaN  ...     NaN     NaN    NaN     NaN    NaN  NaN  NaN  NaN   \n", "4       NaN    NaN  ...     NaN     NaN    NaN     NaN    NaN  NaN  NaN  NaN   \n", "...     ...    ...  ...     ...     ...    ...     ...    ...  ...  ...  ...   \n", "3462  1.065  0.85%  ...  1.0300  -0.34%  0.96%  0.0405  3.95%  NaN  NaN  NaN   \n", "3463  1.065  0.85%  ...  1.0300  -0.34%  0.96%  0.0405  3.95%  NaN  NaN  NaN   \n", "3464  1.065  0.85%  ...  1.0300  -0.34%  0.96%  0.0395  3.85%  NaN  NaN  NaN   \n", "3465  1.065  0.85%  ...  1.0300  -0.34%  0.96%  0.0395  3.85%  NaN  NaN  NaN   \n", "3466  1.065  0.85%  ...  1.0300  -0.34%  0.96%  0.0405  3.95%  NaN  NaN  NaN   \n", "\n", "     时点净值 昨日单位净值  \n", "0     NaN    NaN  \n", "1     NaN    NaN  \n", "2     NaN    NaN  \n", "3     NaN    NaN  \n", "4     NaN    NaN  \n", "...   ...    ...  \n", "3462  NaN    NaN  \n", "3463  NaN    NaN  \n", "3464  NaN    NaN  \n", "3465  NaN    NaN  \n", "3466  NaN    NaN  \n", "\n", "[3467 rows x 32 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取数据\n", "file = os.path.join(get_realtime_data_path(date), \"标的概况.csv\")\n", "# shutil.copy(file, '标的概况/标的概况.csv')\n", "data = pd.read_csv(file, encoding=\"gbk\", dtype=str)\n", "\n", "# 删除 时间 列中 \"时间未更新\" 的行\n", "# 适配旧的历史数据\n", "data = data.query('时间 != \"时间未更新\"').reset_index(drop=True)\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 4, "id": "f66b7146", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:05:03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>378.1亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:05:08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>378.1亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:05:18</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>378.1亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:05:23</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>378.1亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:05:33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>378.1亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3462</th>\n", "      <td>2023-09-01 14:59:54</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>0.37</td>\n", "      <td>344.5万</td>\n", "      <td>3.67亿</td>\n", "      <td>13226</td>\n", "      <td>260.5</td>\n", "      <td>198.3万</td>\n", "      <td>146.2万</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>381.7亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3463</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>0.37</td>\n", "      <td>344.6万</td>\n", "      <td>3.67亿</td>\n", "      <td>13233</td>\n", "      <td>260.4</td>\n", "      <td>198.3万</td>\n", "      <td>146.2万</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>381.7亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3464</th>\n", "      <td>2023-09-01 14:59:59</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>0.37</td>\n", "      <td>344.7万</td>\n", "      <td>3.67亿</td>\n", "      <td>13240</td>\n", "      <td>260.4</td>\n", "      <td>198.3万</td>\n", "      <td>146.4万</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>381.3亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3465</th>\n", "      <td>2023-09-01 15:00:03</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>0.37</td>\n", "      <td>344.7万</td>\n", "      <td>3.67亿</td>\n", "      <td>13242</td>\n", "      <td>260.3</td>\n", "      <td>198.3万</td>\n", "      <td>146.4万</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>381.3亿</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3466</th>\n", "      <td>2023-09-01 15:00:06</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95%</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85%</td>\n", "      <td>0.37</td>\n", "      <td>344.7万</td>\n", "      <td>3.67亿</td>\n", "      <td>13242</td>\n", "      <td>260.3</td>\n", "      <td>198.3万</td>\n", "      <td>146.4万</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>381.7亿</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3467 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                       时间     现价     今开     涨跌     最高     涨幅     最低     昨收  \\\n", "0     2023-09-01 09:05:03    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "1     2023-09-01 09:05:08    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "2     2023-09-01 09:05:18    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "3     2023-09-01 09:05:23    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "4     2023-09-01 09:05:33    NaN    NaN    NaN    NaN    NaN    NaN    NaN   \n", "...                   ...    ...    ...    ...    ...    ...    ...    ...   \n", "3462  2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "3463  2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "3464  2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85%  1.060  1.057   \n", "3465  2023-09-01 15:00:03  1.066  1.064  0.009  1.069  0.85%  1.060  1.057   \n", "3466  2023-09-01 15:00:06  1.067  1.064  0.010  1.069  0.95%  1.060  1.057   \n", "\n", "         均价     振幅    量比      总量     总额     总笔     每笔      外盘      内盘     涨停  \\\n", "0       NaN    NaN   NaN     NaN    NaN    NaN    NaN     NaN     NaN  1.163   \n", "1       NaN    NaN   NaN     NaN    NaN    NaN    NaN     NaN     NaN  1.163   \n", "2       NaN    NaN   NaN     NaN    NaN    NaN    NaN     NaN     NaN  1.163   \n", "3       NaN    NaN   NaN     NaN    NaN    NaN    NaN     NaN     NaN  1.163   \n", "4       NaN    NaN   NaN     NaN    NaN    NaN    NaN     NaN     NaN  1.163   \n", "...     ...    ...   ...     ...    ...    ...    ...     ...     ...    ...   \n", "3462  1.065  0.85%  0.37  344.5万  3.67亿  13226  260.5  198.3万  146.2万  1.163   \n", "3463  1.065  0.85%  0.37  344.6万  3.67亿  13233  260.4  198.3万  146.2万  1.163   \n", "3464  1.065  0.85%  0.37  344.7万  3.67亿  13240  260.4  198.3万  146.4万  1.163   \n", "3465  1.065  0.85%  0.37  344.7万  3.67亿  13242  260.3  198.3万  146.4万  1.163   \n", "3466  1.065  0.85%  0.37  344.7万  3.67亿  13242  260.3  198.3万  146.4万  1.163   \n", "\n", "         跌停      市值  \n", "0     0.951  378.1亿  \n", "1     0.951  378.1亿  \n", "2     0.951  378.1亿  \n", "3     0.951  378.1亿  \n", "4     0.951  378.1亿  \n", "...     ...     ...  \n", "3462  0.951  381.7亿  \n", "3463  0.951  381.7亿  \n", "3464  0.951  381.3亿  \n", "3465  0.951  381.3亿  \n", "3466  0.951  381.7亿  \n", "\n", "[3467 rows x 20 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["cols = [\n", "    '时间',\n", "    '现价',\n", "    '今开',\n", "    '涨跌',\n", "    '最高',\n", "    '涨幅',\n", "    '最低',\n", "    '昨收',\n", "    '均价',\n", "    '振幅',\n", "    '量比',\n", "    '总量',\n", "    '总额',\n", "    '总笔',\n", "    '每笔',\n", "    '外盘',\n", "    '内盘',\n", "    '涨停',\n", "    '跌停',\n", "    '市值'\n", "]\n", "# 需要的列\n", "data = data.loc[:, cols]\n", "data"]}, {"cell_type": "code", "execution_count": 5, "id": "9d6cfd79", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>时间</th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-01 09:05:03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>37810000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-01 09:05:08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>37810000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-01 09:05:18</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>37810000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-01 09:05:23</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>37810000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-01 09:05:33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>37810000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3462</th>\n", "      <td>2023-09-01 14:59:54</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3463</th>\n", "      <td>2023-09-01 14:59:57</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3464</th>\n", "      <td>2023-09-01 14:59:59</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38130000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3465</th>\n", "      <td>2023-09-01 15:00:03</td>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13242.0</td>\n", "      <td>260.3</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38130000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3466</th>\n", "      <td>2023-09-01 15:00:06</td>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13242.0</td>\n", "      <td>260.3</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3467 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                       时间     现价     今开     涨跌     最高    涨幅     最低     昨收  \\\n", "0     2023-09-01 09:05:03    NaN    NaN    NaN    NaN   NaN    NaN    NaN   \n", "1     2023-09-01 09:05:08    NaN    NaN    NaN    NaN   NaN    NaN    NaN   \n", "2     2023-09-01 09:05:18    NaN    NaN    NaN    NaN   NaN    NaN    NaN   \n", "3     2023-09-01 09:05:23    NaN    NaN    NaN    NaN   NaN    NaN    NaN   \n", "4     2023-09-01 09:05:33    NaN    NaN    NaN    NaN   NaN    NaN    NaN   \n", "...                   ...    ...    ...    ...    ...   ...    ...    ...   \n", "3462  2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057   \n", "3463  2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057   \n", "3464  2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85  1.060  1.057   \n", "3465  2023-09-01 15:00:03  1.066  1.064  0.009  1.069  0.85  1.060  1.057   \n", "3466  2023-09-01 15:00:06  1.067  1.064  0.010  1.069  0.95  1.060  1.057   \n", "\n", "         均价    振幅    量比         总量           总额       总笔     每笔         外盘  \\\n", "0       NaN   NaN   NaN        NaN          NaN      NaN    NaN        NaN   \n", "1       NaN   NaN   NaN        NaN          NaN      NaN    NaN        NaN   \n", "2       NaN   NaN   NaN        NaN          NaN      NaN    NaN        NaN   \n", "3       NaN   NaN   NaN        NaN          NaN      NaN    NaN        NaN   \n", "4       NaN   NaN   NaN        NaN          NaN      NaN    NaN        NaN   \n", "...     ...   ...   ...        ...          ...      ...    ...        ...   \n", "3462  1.065  0.85  0.37  3445000.0  367000000.0  13226.0  260.5  1983000.0   \n", "3463  1.065  0.85  0.37  3446000.0  367000000.0  13233.0  260.4  1983000.0   \n", "3464  1.065  0.85  0.37  3447000.0  367000000.0  13240.0  260.4  1983000.0   \n", "3465  1.065  0.85  0.37  3447000.0  367000000.0  13242.0  260.3  1983000.0   \n", "3466  1.065  0.85  0.37  3447000.0  367000000.0  13242.0  260.3  1983000.0   \n", "\n", "             内盘     涨停     跌停             市值  \n", "0           NaN  1.163  0.951  37810000000.0  \n", "1           NaN  1.163  0.951  37810000000.0  \n", "2           NaN  1.163  0.951  37810000000.0  \n", "3           NaN  1.163  0.951  37810000000.0  \n", "4           NaN  1.163  0.951  37810000000.0  \n", "...         ...    ...    ...            ...  \n", "3462  1462000.0  1.163  0.951  38170000000.0  \n", "3463  1462000.0  1.163  0.951  38170000000.0  \n", "3464  1464000.0  1.163  0.951  38130000000.0  \n", "3465  1464000.0  1.163  0.951  38130000000.0  \n", "3466  1464000.0  1.163  0.951  38170000000.0  \n", "\n", "[3467 rows x 20 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 格式处理\n", "\n", "# %处理\n", "unit_cols_0 = ['涨幅','振幅']\n", "data.loc[:, unit_cols_0] = data.loc[:, unit_cols_0].replace(\"%\", \"\", regex=True).astype(float)\n", "    \n", "# 单位处理\n", "unit_cols_1 = ['总量','总额', '内盘', '外盘', '市值']\n", "data.loc[:, unit_cols_1] = data.loc[:, unit_cols_1].applymap(\n", "    lambda x: 10000 * (float(x.replace(\"万\", \"\")))\n", "    if \"万\" in str(x)\n", "    else 1e8 * (float(x.replace(\"亿\", \"\")))\n", "    if \"亿\" in str(x)\n", "    else float(x)\n", ")\n", "\n", "# - 处理\n", "unit_cols_2 = ['每笔','总笔']\n", "data.loc[:, unit_cols_2] = data.loc[:, unit_cols_2].applymap(lambda x: np.nan if \"-\" in str(x) else float(x))\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 6, "id": "7595f96a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>19450.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>38166.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38060000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>38166.0</td>\n", "      <td>33683.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:11</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.33</td>\n", "      <td>90665.0</td>\n", "      <td>9648000.0</td>\n", "      <td>501.0</td>\n", "      <td>181.0</td>\n", "      <td>39705.0</td>\n", "      <td>50960.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38060000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3441000.0</td>\n", "      <td>366000000.0</td>\n", "      <td>13212.0</td>\n", "      <td>260.5</td>\n", "      <td>1979000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1982000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38130000000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3119 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:11  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:15  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:48  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:11  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:15  0.19  2.33    90665.0    9648000.0    501.0  181.0   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:48  0.85  0.37  3441000.0  366000000.0  13212.0  260.5   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3446000.0  367000000.0  13233.0  260.4   \n", "2023-09-01 14:59:59  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "\n", "                            外盘         内盘     涨停     跌停             市值  \n", "时间                                                                      \n", "2023-09-01 09:30:03    19450.0    27273.0  1.163  0.951  38030000000.0  \n", "2023-09-01 09:30:06    38166.0    27273.0  1.163  0.951  38060000000.0  \n", "2023-09-01 09:30:09    38166.0    33683.0  1.163  0.951  38030000000.0  \n", "2023-09-01 09:30:11    38166.0    49420.0  1.163  0.951  38030000000.0  \n", "2023-09-01 09:30:15    39705.0    50960.0  1.163  0.951  38060000000.0  \n", "...                        ...        ...    ...    ...            ...  \n", "2023-09-01 14:59:48  1979000.0  1462000.0  1.163  0.951  38170000000.0  \n", "2023-09-01 14:59:51  1982000.0  1462000.0  1.163  0.951  38170000000.0  \n", "2023-09-01 14:59:54  1983000.0  1462000.0  1.163  0.951  38170000000.0  \n", "2023-09-01 14:59:57  1983000.0  1462000.0  1.163  0.951  38170000000.0  \n", "2023-09-01 14:59:59  1983000.0  1464000.0  1.163  0.951  38130000000.0  \n", "\n", "[3119 rows x 19 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 格式化时间\n", "date = data[\"时间\"][0][:10]\n", "data[\"时间\"] = pd.to_datetime(data[\"时间\"])\n", "data.set_index(\"时间\", inplace=True)\n", "data = data.between_time(\"09:30:00\", \"15:00:00\")\n", "\n", "# # 交易时间内的数据数量检查\n", "# if len(data) == 0:\n", "#     return None\n", "\n", "data"]}, {"cell_type": "code", "execution_count": 7, "id": "a7cce6bc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>std_dts</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                     std_dts\n", "时间                          \n", "2023-09-01 09:30:03        1\n", "2023-09-01 09:30:06        1\n", "2023-09-01 09:30:09        1\n", "2023-09-01 09:30:12        1\n", "2023-09-01 09:30:15        1\n", "...                      ...\n", "2023-09-01 14:59:48        1\n", "2023-09-01 14:59:51        1\n", "2023-09-01 14:59:54        1\n", "2023-09-01 14:59:57        1\n", "2023-09-01 15:00:00        1\n", "\n", "[4800 rows x 1 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from tool import get_time_point\n", "# 标准化时间\n", "dts = get_time_point(date)\n", "dts[\"std_dts\"] = 1\n", "dts"]}, {"cell_type": "code", "execution_count": 8, "id": "5609ef8f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "      <th>std_dts</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>19450.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>38166.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38060000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>38166.0</td>\n", "      <td>33683.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:11</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38030000000.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1982000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38170000000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>38130000000.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7098 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:11  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:12    NaN    NaN    NaN    NaN   NaN    NaN    NaN    NaN   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "2023-09-01 15:00:00    NaN    NaN    NaN    NaN   NaN    NaN    NaN    NaN   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:11  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:12   NaN   NaN        NaN          NaN      NaN    NaN   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3446000.0  367000000.0  13233.0  260.4   \n", "2023-09-01 14:59:59  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "2023-09-01 15:00:00   NaN   NaN        NaN          NaN      NaN    NaN   \n", "\n", "                            外盘         内盘     涨停     跌停             市值  \\\n", "时间                                                                       \n", "2023-09-01 09:30:03    19450.0    27273.0  1.163  0.951  38030000000.0   \n", "2023-09-01 09:30:06    38166.0    27273.0  1.163  0.951  38060000000.0   \n", "2023-09-01 09:30:09    38166.0    33683.0  1.163  0.951  38030000000.0   \n", "2023-09-01 09:30:11    38166.0    49420.0  1.163  0.951  38030000000.0   \n", "2023-09-01 09:30:12        NaN        NaN    NaN    NaN            NaN   \n", "...                        ...        ...    ...    ...            ...   \n", "2023-09-01 14:59:51  1982000.0  1462000.0  1.163  0.951  38170000000.0   \n", "2023-09-01 14:59:54  1983000.0  1462000.0  1.163  0.951  38170000000.0   \n", "2023-09-01 14:59:57  1983000.0  1462000.0  1.163  0.951  38170000000.0   \n", "2023-09-01 14:59:59  1983000.0  1464000.0  1.163  0.951  38130000000.0   \n", "2023-09-01 15:00:00        NaN        NaN    NaN    NaN            NaN   \n", "\n", "                     std_dts  \n", "时间                            \n", "2023-09-01 09:30:03      1.0  \n", "2023-09-01 09:30:06      1.0  \n", "2023-09-01 09:30:09      1.0  \n", "2023-09-01 09:30:11      NaN  \n", "2023-09-01 09:30:12      1.0  \n", "...                      ...  \n", "2023-09-01 14:59:51      1.0  \n", "2023-09-01 14:59:54      1.0  \n", "2023-09-01 14:59:57      1.0  \n", "2023-09-01 14:59:59      NaN  \n", "2023-09-01 15:00:00      1.0  \n", "\n", "[7098 rows x 20 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.merge(data, dts, how=\"outer\", left_index=True, right_index=True, sort=True)\n", "data"]}, {"cell_type": "code", "execution_count": 9, "id": "26dd0c8e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "      <th>std_dts</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>19450.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>38166.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.806000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>38166.0</td>\n", "      <td>33683.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:11</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1982000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:59</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.813000e+10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.813000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7098 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:11  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:12  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:59  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "2023-09-01 15:00:00  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:11  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:12  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3446000.0  367000000.0  13233.0  260.4   \n", "2023-09-01 14:59:59  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "2023-09-01 15:00:00  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "\n", "                            外盘         内盘     涨停     跌停            市值  std_dts  \n", "时间                                                                              \n", "2023-09-01 09:30:03    19450.0    27273.0  1.163  0.951  3.803000e+10      1.0  \n", "2023-09-01 09:30:06    38166.0    27273.0  1.163  0.951  3.806000e+10      1.0  \n", "2023-09-01 09:30:09    38166.0    33683.0  1.163  0.951  3.803000e+10      1.0  \n", "2023-09-01 09:30:11    38166.0    49420.0  1.163  0.951  3.803000e+10      NaN  \n", "2023-09-01 09:30:12    38166.0    49420.0  1.163  0.951  3.803000e+10      1.0  \n", "...                        ...        ...    ...    ...           ...      ...  \n", "2023-09-01 14:59:51  1982000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:54  1983000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:57  1983000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:59  1983000.0  1464000.0  1.163  0.951  3.813000e+10      NaN  \n", "2023-09-01 15:00:00  1983000.0  1464000.0  1.163  0.951  3.813000e+10      1.0  \n", "\n", "[7098 rows x 20 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["std_dts = data[\"std_dts\"].copy()\n", "data.fillna(method=\"ffill\", inplace=True)\n", "data[\"std_dts\"] = std_dts\n", "data"]}, {"cell_type": "code", "execution_count": 10, "id": "da8545a9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "      <th>std_dts</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>19450.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>38166.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.806000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>38166.0</td>\n", "      <td>33683.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.33</td>\n", "      <td>90665.0</td>\n", "      <td>9648000.0</td>\n", "      <td>501.0</td>\n", "      <td>181.0</td>\n", "      <td>39705.0</td>\n", "      <td>50960.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.806000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3441000.0</td>\n", "      <td>366000000.0</td>\n", "      <td>13212.0</td>\n", "      <td>260.5</td>\n", "      <td>1979000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1982000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.813000e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:12  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:15  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:48  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 15:00:00  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:12  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:15  0.19  2.33    90665.0    9648000.0    501.0  181.0   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:48  0.85  0.37  3441000.0  366000000.0  13212.0  260.5   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3446000.0  367000000.0  13233.0  260.4   \n", "2023-09-01 15:00:00  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "\n", "                            外盘         内盘     涨停     跌停            市值  std_dts  \n", "时间                                                                              \n", "2023-09-01 09:30:03    19450.0    27273.0  1.163  0.951  3.803000e+10      1.0  \n", "2023-09-01 09:30:06    38166.0    27273.0  1.163  0.951  3.806000e+10      1.0  \n", "2023-09-01 09:30:09    38166.0    33683.0  1.163  0.951  3.803000e+10      1.0  \n", "2023-09-01 09:30:12    38166.0    49420.0  1.163  0.951  3.803000e+10      1.0  \n", "2023-09-01 09:30:15    39705.0    50960.0  1.163  0.951  3.806000e+10      1.0  \n", "...                        ...        ...    ...    ...           ...      ...  \n", "2023-09-01 14:59:48  1979000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:51  1982000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:54  1983000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 14:59:57  1983000.0  1462000.0  1.163  0.951  3.817000e+10      1.0  \n", "2023-09-01 15:00:00  1983000.0  1464000.0  1.163  0.951  3.813000e+10      1.0  \n", "\n", "[4800 rows x 20 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data.query(\"std_dts == 1\",inplace=True)\n", "data"]}, {"cell_type": "code", "execution_count": 11, "id": "6ef638ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>现价</th>\n", "      <th>今开</th>\n", "      <th>涨跌</th>\n", "      <th>最高</th>\n", "      <th>涨幅</th>\n", "      <th>最低</th>\n", "      <th>昨收</th>\n", "      <th>均价</th>\n", "      <th>振幅</th>\n", "      <th>量比</th>\n", "      <th>总量</th>\n", "      <th>总额</th>\n", "      <th>总笔</th>\n", "      <th>每笔</th>\n", "      <th>外盘</th>\n", "      <th>内盘</th>\n", "      <th>涨停</th>\n", "      <th>跌停</th>\n", "      <th>市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>时间</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-01 09:30:03</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.20</td>\n", "      <td>46723.0</td>\n", "      <td>4971000.0</td>\n", "      <td>277.0</td>\n", "      <td>168.7</td>\n", "      <td>19450.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:06</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.68</td>\n", "      <td>65439.0</td>\n", "      <td>6963000.0</td>\n", "      <td>344.0</td>\n", "      <td>190.2</td>\n", "      <td>38166.0</td>\n", "      <td>27273.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.806000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:09</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>1.85</td>\n", "      <td>71849.0</td>\n", "      <td>7645000.0</td>\n", "      <td>422.0</td>\n", "      <td>170.3</td>\n", "      <td>38166.0</td>\n", "      <td>33683.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:12</th>\n", "      <td>1.063</td>\n", "      <td>1.064</td>\n", "      <td>0.006</td>\n", "      <td>1.065</td>\n", "      <td>0.57</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.25</td>\n", "      <td>87586.0</td>\n", "      <td>9320000.0</td>\n", "      <td>471.0</td>\n", "      <td>186.0</td>\n", "      <td>38166.0</td>\n", "      <td>49420.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.803000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 09:30:15</th>\n", "      <td>1.064</td>\n", "      <td>1.064</td>\n", "      <td>0.007</td>\n", "      <td>1.065</td>\n", "      <td>0.66</td>\n", "      <td>1.063</td>\n", "      <td>1.057</td>\n", "      <td>1.064</td>\n", "      <td>0.19</td>\n", "      <td>2.33</td>\n", "      <td>90665.0</td>\n", "      <td>9648000.0</td>\n", "      <td>501.0</td>\n", "      <td>181.0</td>\n", "      <td>39705.0</td>\n", "      <td>50960.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.806000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:48</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3441000.0</td>\n", "      <td>366000000.0</td>\n", "      <td>13212.0</td>\n", "      <td>260.5</td>\n", "      <td>1979000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:51</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3444000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13224.0</td>\n", "      <td>260.5</td>\n", "      <td>1982000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:54</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3445000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13226.0</td>\n", "      <td>260.5</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 14:59:57</th>\n", "      <td>1.067</td>\n", "      <td>1.064</td>\n", "      <td>0.010</td>\n", "      <td>1.069</td>\n", "      <td>0.95</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3446000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13233.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1462000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.817000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 15:00:00</th>\n", "      <td>1.066</td>\n", "      <td>1.064</td>\n", "      <td>0.009</td>\n", "      <td>1.069</td>\n", "      <td>0.85</td>\n", "      <td>1.060</td>\n", "      <td>1.057</td>\n", "      <td>1.065</td>\n", "      <td>0.85</td>\n", "      <td>0.37</td>\n", "      <td>3447000.0</td>\n", "      <td>367000000.0</td>\n", "      <td>13240.0</td>\n", "      <td>260.4</td>\n", "      <td>1983000.0</td>\n", "      <td>1464000.0</td>\n", "      <td>1.163</td>\n", "      <td>0.951</td>\n", "      <td>3.813000e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4800 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                        现价     今开     涨跌     最高    涨幅     最低     昨收     均价  \\\n", "时间                                                                           \n", "2023-09-01 09:30:03  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:06  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "2023-09-01 09:30:09  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:12  1.063  1.064  0.006  1.065  0.57  1.063  1.057  1.064   \n", "2023-09-01 09:30:15  1.064  1.064  0.007  1.065  0.66  1.063  1.057  1.064   \n", "...                    ...    ...    ...    ...   ...    ...    ...    ...   \n", "2023-09-01 14:59:48  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:51  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:54  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 14:59:57  1.067  1.064  0.010  1.069  0.95  1.060  1.057  1.065   \n", "2023-09-01 15:00:00  1.066  1.064  0.009  1.069  0.85  1.060  1.057  1.065   \n", "\n", "                       振幅    量比         总量           总额       总笔     每笔  \\\n", "时间                                                                        \n", "2023-09-01 09:30:03  0.19  1.20    46723.0    4971000.0    277.0  168.7   \n", "2023-09-01 09:30:06  0.19  1.68    65439.0    6963000.0    344.0  190.2   \n", "2023-09-01 09:30:09  0.19  1.85    71849.0    7645000.0    422.0  170.3   \n", "2023-09-01 09:30:12  0.19  2.25    87586.0    9320000.0    471.0  186.0   \n", "2023-09-01 09:30:15  0.19  2.33    90665.0    9648000.0    501.0  181.0   \n", "...                   ...   ...        ...          ...      ...    ...   \n", "2023-09-01 14:59:48  0.85  0.37  3441000.0  366000000.0  13212.0  260.5   \n", "2023-09-01 14:59:51  0.85  0.37  3444000.0  367000000.0  13224.0  260.5   \n", "2023-09-01 14:59:54  0.85  0.37  3445000.0  367000000.0  13226.0  260.5   \n", "2023-09-01 14:59:57  0.85  0.37  3446000.0  367000000.0  13233.0  260.4   \n", "2023-09-01 15:00:00  0.85  0.37  3447000.0  367000000.0  13240.0  260.4   \n", "\n", "                            外盘         内盘     涨停     跌停            市值  \n", "时间                                                                     \n", "2023-09-01 09:30:03    19450.0    27273.0  1.163  0.951  3.803000e+10  \n", "2023-09-01 09:30:06    38166.0    27273.0  1.163  0.951  3.806000e+10  \n", "2023-09-01 09:30:09    38166.0    33683.0  1.163  0.951  3.803000e+10  \n", "2023-09-01 09:30:12    38166.0    49420.0  1.163  0.951  3.803000e+10  \n", "2023-09-01 09:30:15    39705.0    50960.0  1.163  0.951  3.806000e+10  \n", "...                        ...        ...    ...    ...           ...  \n", "2023-09-01 14:59:48  1979000.0  1462000.0  1.163  0.951  3.817000e+10  \n", "2023-09-01 14:59:51  1982000.0  1462000.0  1.163  0.951  3.817000e+10  \n", "2023-09-01 14:59:54  1983000.0  1462000.0  1.163  0.951  3.817000e+10  \n", "2023-09-01 14:59:57  1983000.0  1462000.0  1.163  0.951  3.817000e+10  \n", "2023-09-01 15:00:00  1983000.0  1464000.0  1.163  0.951  3.813000e+10  \n", "\n", "[4800 rows x 19 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data.drop(\"std_dts\", axis=1, inplace=True)\n", "data"]}, {"cell_type": "code", "execution_count": 15, "id": "a70f08c3", "metadata": {}, "outputs": [], "source": ["data = data.astype(float)"]}, {"cell_type": "code", "execution_count": 16, "id": "0dd77abc", "metadata": {}, "outputs": [{"data": {"text/plain": ["现价    float64\n", "今开    float64\n", "涨跌    float64\n", "最高    float64\n", "涨幅    float64\n", "最低    float64\n", "昨收    float64\n", "均价    float64\n", "振幅    float64\n", "量比    float64\n", "总量    float64\n", "总额    float64\n", "总笔    float64\n", "每笔    float64\n", "外盘    float64\n", "内盘    float64\n", "涨停    float64\n", "跌停    float64\n", "市值    float64\n", "dtype: object"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "da0a8614", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9a44d311", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "bce9384e", "metadata": {}, "outputs": [], "source": ["# input()"]}, {"cell_type": "code", "execution_count": null, "id": "78419ff2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}