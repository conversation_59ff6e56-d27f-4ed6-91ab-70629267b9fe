import pandas as pd
import numpy as np
from datetime import timedelta

from ...tool import top_to_cols
from ...base_class import zb_feature

"""
标识串及含义：
B  限价买入                S  限价卖出
1B 市价买入               1S 市价卖出
UB 本方最优市价买入  US 本方最优市价卖出
BC 撤买入单               SC 撤卖出单
"""
# 成交类型
ORDER_TYPES = ["B", "S", "BC", "SC", "1B", "1S", "UB", "US"]
B, S, BC, SC, _1B, _1S, UB, US = range(len(ORDER_TYPES))


class zbwt_001(zb_feature):
    """
    1 + 150

    '时间' +
    ['ZBWT_区间委托', 'ZBWT_区间委买', 'ZBWT_区间委买1', 'ZBWT_区间委买优', 'ZBWT_区间委卖', 'ZBWT_区间委卖1', 'ZBWT_区间委卖优', 'ZBWT_区间委撤', 'ZBWT_区间委撤买', 'ZBWT_区间委撤卖', 'ZBWT_区间手数', 'ZBWT_区间手数波动率', 'ZBWT_区间买手', 'ZBWT_区间买手波动率', 'ZBWT_区间买手1', 'ZBWT_区间买手优', 'ZBWT_区间卖手', 'ZBWT_区间卖手波动率', 'ZBWT_区间卖手1', 'ZBWT_区间卖手优', 'ZBWT_区间撤手', 'ZBWT_区间撤手买', 'ZBWT_区间撤手卖', 'ZBWT_区间撤额', 'ZBWT_区间撤额买', 'ZBWT_区间撤额卖', 'ZBWT_累计委托', 'ZBWT_累计委买', 'ZBWT_累计委买1', 'ZBWT_累计委买优', 'ZBWT_累计委卖', 'ZBWT_累计委卖1', 'ZBWT_累计委卖优', 'ZBWT_累计委撤', 'ZBWT_累计委撤买', 'ZBWT_累计委撤卖', 'ZBWT_累计手数', 'ZBWT_累计买手', 'ZBWT_累计买手1', 'ZBWT_累计买手优', 'ZBWT_累计卖手', 'ZBWT_累计卖手1', 'ZBWT_累计卖手优', 'ZBWT_累计撤手', 'ZBWT_累计撤手买', 'ZBWT_累计撤手卖', 'ZBWT_区间撤VWAP', 'ZBWT_区间撤VWAP买', 'ZBWT_区间撤VWAP卖', 'ZBWT_累计撤VWAP', 'ZBWT_累计撤VWAP买', 'ZBWT_累计撤VWAP卖', 'ZBWT_区间撤比', 'ZBWT_累计撤比', 'ZBWT_区间撤比手', 'ZBWT_累计撤比手', 'ZBWT_区间均手', 'ZBWT_区间均手买', 'ZBWT_区间均手买1', 'ZBWT_区间均手买优', 'ZBWT_区间均手卖', 'ZBWT_区间均手卖1', 'ZBWT_区间均手卖优', 'ZBWT_累计均手', 'ZBWT_累计均手买', 'ZBWT_累计均手买1', 'ZBWT_累计均手买优', 'ZBWT_累计均手卖', 'ZBWT_累计均手卖1', 'ZBWT_累计均手卖优', 'ZBWT_委买手1价', 'ZBWT_委买手1手', 'ZBWT_委买手2价', 'ZBWT_委买手2手', 'ZBWT_委买手3价', 'ZBWT_委买手3手', 'ZBWT_委买手4价', 'ZBWT_委买手4手', 'ZBWT_委买手5价', 'ZBWT_委买手5手', 'ZBWT_委买手6价', 'ZBWT_委买手6手', 'ZBWT_委买手7价', 'ZBWT_委买手7手', 'ZBWT_委买手8价', 'ZBWT_委买手8手', 'ZBWT_委买手9价', 'ZBWT_委买手9手', 'ZBWT_委买手10价', 'ZBWT_委买手10手', 'ZBWT_委买价1价', 'ZBWT_委买价1手', 'ZBWT_委买价2价', 'ZBWT_委买价2手', 'ZBWT_委买价3价', 'ZBWT_委买价3手', 'ZBWT_委买价4价', 'ZBWT_委买价4手', 'ZBWT_委买价5价', 'ZBWT_委买价5手', 'ZBWT_委买价6价', 'ZBWT_委买价6手', 'ZBWT_委买价7价', 'ZBWT_委买价7手', 'ZBWT_委买价8价', 'ZBWT_委买价8手', 'ZBWT_委买价9价', 'ZBWT_委买价9手', 'ZBWT_委买价10价', 'ZBWT_委买价10手', 'ZBWT_委卖手1价', 'ZBWT_委卖手1手', 'ZBWT_委卖手2价', 'ZBWT_委卖手2手', 'ZBWT_委卖手3价', 'ZBWT_委卖手3手', 'ZBWT_委卖手4价', 'ZBWT_委卖手4手', 'ZBWT_委卖手5价', 'ZBWT_委卖手5手', 'ZBWT_委卖手6价', 'ZBWT_委卖手6手', 'ZBWT_委卖手7价', 'ZBWT_委卖手7手', 'ZBWT_委卖手8价', 'ZBWT_委卖手8手', 'ZBWT_委卖手9价', 'ZBWT_委卖手9手', 'ZBWT_委卖手10价', 'ZBWT_委卖手10手', 'ZBWT_委卖价1价', 'ZBWT_委卖价1手', 'ZBWT_委卖价2价', 'ZBWT_委卖价2手', 'ZBWT_委卖价3价', 'ZBWT_委卖价3手', 'ZBWT_委卖价4价', 'ZBWT_委卖价4手', 'ZBWT_委卖价5价', 'ZBWT_委卖价5手', 'ZBWT_委卖价6价', 'ZBWT_委卖价6手', 'ZBWT_委卖价7价', 'ZBWT_委卖价7手', 'ZBWT_委卖价8价', 'ZBWT_委卖价8手', 'ZBWT_委卖价9价', 'ZBWT_委卖价9手', 'ZBWT_委卖价10价', 'ZBWT_委卖价10手']

    """

    def __init__(self, kwargs: dict = {"top_n": 5}):
        super().__init__(kwargs)

    def top_func(self, end_dt: str, data: pd.DataFrame, top_n: int):
        """
        买卖单中最大 数量top
        买卖单中最 高/低价top
        """
        # 时间筛选
        begin_dt = end_dt-timedelta(seconds=3)
        time_data = data[(data.index >= begin_dt) & (data.index < end_dt)]
        if len(time_data) == 0:
            return pd.DataFrame()

        # 291 ms
        # 买卖筛选
        buy_trade = time_data.loc[time_data['类型'] == B, :]
        sell_trade = time_data.loc[time_data['类型'] == S, :]

        # 排序
        _buy_type_name = "买"
        _sell_type_name = "卖"
        sort_by_hand = ["手", "价格"]
        sort_by_price = ["价格", "手"]
        buy_trade_vol = buy_trade.sort_values(
            sort_by_hand, ascending=[False, False]).iloc[:top_n, 2:4].reset_index(drop=True)
        sell_trade_vol = sell_trade.sort_values(
            sort_by_hand, ascending=[False, True]).iloc[:top_n, 2:4].reset_index(drop=True)
        buy_trade_price = buy_trade.sort_values(
            sort_by_price, ascending=[False, False]).iloc[:top_n, 2:4].reset_index(drop=True)
        sell_trade_price = sell_trade.sort_values(
            sort_by_price, ascending=[True, False]).iloc[:top_n, 2:4].reset_index(drop=True)

        # return pd.DataFrame()
        # 合并返回
        return pd.concat([
            top_to_cols(buy_trade_vol, _buy_type_name,
                         sort_by_hand, end_dt, top_n, if_trade=False),
            top_to_cols(buy_trade_price, _buy_type_name,
                        sort_by_price, end_dt, top_n, if_trade=False),
            top_to_cols(sell_trade_vol, _sell_type_name,
                        sort_by_hand, end_dt, top_n, if_trade=False),
            top_to_cols(sell_trade_price, _sell_type_name,
                        sort_by_price, end_dt, top_n, if_trade=False),
        ], axis=1)

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据
        """
        data["价格"] = data["价格"].astype(float)

        # 涨跌停，一买卖价格处理
        # data["价格"].fillna(-1, inplace=True)

        # 时间
        data["时间"] = pd.to_datetime(data["时间"])

        # 在委时间
        data.loc[data["在委时间"].isna(), "在委时间"] = "0"
        data["在委时间"] = data["在委时间"].apply(
            lambda x: int(x[:-1])
            if "s" in x
            else int(x[:-1]) * 60
            if "m" in x
            else int(x[:-1]) * 60 * 60
            if "h" in x
            else int(x)
        )

        # 手 卖出会存在小数
        data["手"] = data["手"].astype(float).astype(int)

        # 类型
        data["类型"] = data["类型"].apply(lambda x: ORDER_TYPES.index(x))

        data = data.set_index("时间")

        return data

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        # 切片交易时间内的数据
        data = pd.concat([data.between_time("09:30:00", "11:30:00"),
                          data.between_time("13:00:00", "14:55:00")])

        if len(data) == 0:
            return None

        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        data.loc[pd.to_datetime(date_str + "14:55:01")] = np.nan

        # 订单数量
        data["区间委托"] = 1

        data["区间委买"] = data["类型"].apply(
            lambda x: 1 if x in [B, _1B, UB] else 0)
        # data["区间委买1"] = data["类型"].apply(lambda x: 1 if x == _1B else 0)
        # data["区间委买优"] = data["类型"].apply(lambda x: 1 if x == UB else 0)

        data["区间委卖"] = data["类型"].apply(
            lambda x: 1 if x in [S, _1S, US] else 0)
        # data["区间委卖1"] = data["类型"].apply(lambda x: 1 if x == _1S else 0)
        # data["区间委卖优"] = data["类型"].apply(lambda x: 1 if x == US else 0)

        data["区间委撤"] = data["类型"].apply(lambda x: 1 if x in [BC, SC] else 0)
        data["区间委撤买"] = data["类型"].apply(lambda x: 1 if x == BC else 0)
        data["区间委撤卖"] = data["类型"].apply(lambda x: 1 if x == SC else 0)

        # 手数
        data["区间手数"] = data["手"]
        data["区间手数波动率"] = data["手"]

        data["区间买手"] = data["手"] * data["区间委买"]
        data["区间买手波动率"] = data["区间买手"]
        # data["区间买手1"] = data["手"] * data["区间委买1"]
        # data["区间买手优"] = data["手"] * data["区间委买优"]

        data["区间卖手"] = data["手"] * data["区间委卖"]
        data["区间卖手波动率"] = data["区间卖手"]
        # data["区间卖手1"] = data["手"] * data["区间委卖1"]
        # data["区间卖手优"] = data["手"] * data["区间委卖优"]

        data["区间撤手"] = data["手"] * data["区间委撤"]
        data["区间撤手买"] = data["手"] * data["区间委撤买"]
        data["区间撤手卖"] = data["手"] * data["区间委撤卖"]

        # 额
        data["区间撤额"] = data["区间撤手"] * data["价格"]
        data["区间撤额买"] = data["区间撤手买"] * data["价格"]
        data["区间撤额卖"] = data["区间撤手卖"] * data["价格"]

        std_data = data.resample("3s", label="right").apply(
            {
                "区间委托": 'sum',
                "区间委买": 'sum',
                # "区间委买1": 'sum',
                # "区间委买优": 'sum',
                "区间委卖": 'sum',
                # "区间委卖1": 'sum',
                # "区间委卖优": 'sum',
                "区间委撤": 'sum',
                "区间委撤买": 'sum',
                "区间委撤卖": 'sum',
                "区间手数": 'sum',
                "区间手数波动率": 'std',
                "区间买手": 'sum',
                "区间买手波动率": lambda x: np.std(x[x != 0]),
                # "区间买手1": 'sum',
                # "区间买手优": 'sum',
                "区间卖手": 'sum',
                "区间卖手波动率": lambda x: np.std(x[x != 0]),
                # "区间卖手1": 'sum',
                # "区间卖手优": 'sum',
                "区间撤手": 'sum',
                "区间撤手买": 'sum',
                "区间撤手卖": 'sum',
                "区间撤额": 'sum',
                "区间撤额买": 'sum',
                "区间撤额卖": 'sum',
            }
        )

        # 删除最后一行 第一行
        std_data = std_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = std_data.between_time("09:30:01", "11:30:00")
        pm = std_data.between_time("13:00:01", "14:55:00")
        std_data = std_data.loc[am.index.union(pm.index), :]

        # 增加累计数据
        std_data["累计委托"] = std_data["区间委托"].cumsum()
        std_data["累计委买"] = std_data["区间委买"].cumsum()
        # std_data["累计委买1"] = std_data["区间委买1"].cumsum()
        # std_data["累计委买优"] = std_data["区间委买优"].cumsum()
        std_data["累计委卖"] = std_data["区间委卖"].cumsum()
        # std_data["累计委卖1"] = std_data["区间委卖1"].cumsum()
        # std_data["累计委卖优"] = std_data["区间委卖优"].cumsum()
        std_data["累计委撤"] = std_data["区间委撤"].cumsum()
        std_data["累计委撤买"] = std_data["区间委撤买"].cumsum()
        std_data["累计委撤卖"] = std_data["区间委撤卖"].cumsum()

        std_data["累计手数"] = std_data["区间手数"].cumsum()
        std_data["累计买手"] = std_data["区间买手"].cumsum()
        # std_data["累计买手1"] = std_data["区间买手1"].cumsum()
        # std_data["累计买手优"] = std_data["区间买手优"].cumsum()
        std_data["累计卖手"] = std_data["区间卖手"].cumsum()
        # std_data["累计卖手1"] = std_data["区间卖手1"].cumsum()
        # std_data["累计卖手优"] = std_data["区间卖手优"].cumsum()
        std_data["累计撤手"] = std_data["区间撤手"].cumsum()
        std_data["累计撤手买"] = std_data["区间撤手买"].cumsum()
        std_data["累计撤手卖"] = std_data["区间撤手卖"].cumsum()

        std_data["区间撤VWAP"] = std_data["区间撤额"] / std_data["区间撤手"]
        std_data["区间撤VWAP买"] = std_data["区间撤额买"] / std_data["区间撤手买"]
        std_data["区间撤VWAP卖"] = std_data["区间撤额卖"] / std_data["区间撤手卖"]
        std_data["累计撤VWAP"] = (std_data["区间撤额"].cumsum()) / (std_data["累计撤手"])
        std_data["累计撤VWAP买"] = (
            std_data["区间撤额买"].cumsum()) / (std_data["累计撤手买"])
        std_data["累计撤VWAP卖"] = (
            std_data["区间撤额卖"].cumsum()) / (std_data["累计撤手卖"])

        std_data["区间撤比"] = std_data["区间委撤"] / std_data["区间委托"]
        std_data["累计撤比"] = std_data["累计委撤"] / std_data["累计委托"]
        std_data["区间撤比手"] = std_data["区间撤手"] / std_data["区间手数"]
        std_data["累计撤比手"] = std_data["累计撤手"] / std_data["累计手数"]

        std_data["区间均手"] = std_data["区间手数"] / std_data["区间委托"]
        std_data["区间均手买"] = std_data["区间买手"] / std_data["区间委买"]
        # std_data["区间均手买1"] = std_data["区间买手1"] / std_data["区间委买1"]
        # std_data["区间均手买优"] = std_data["区间买手优"] / std_data["区间委买优"]
        std_data["区间均手卖"] = std_data["区间卖手"] / std_data["区间委卖"]
        # std_data["区间均手卖1"] = std_data["区间卖手1"] / std_data["区间委卖1"]
        # std_data["区间均手卖优"] = std_data["区间卖手优"] / std_data["区间委卖优"]

        std_data["累计均手"] = std_data["累计手数"] / std_data["累计委托"]

        std_data["累计均手买"] = std_data["累计买手"] / std_data["累计委买"]
        # std_data["累计均手买1"] = std_data["累计买手1"] / std_data["累计委买1"]
        # std_data["累计均手买优"] = std_data["累计买手优"] / std_data["累计委买优"]
        std_data["累计均手卖"] = std_data["累计卖手"] / std_data["累计委卖"]
        # std_data["累计均手卖1"] = std_data["累计卖手1"] / std_data["累计委卖1"]
        # std_data["累计均手卖优"] = std_data["累计卖手优"] / std_data["累计委卖优"]

        # 不允许存在nan的列
        nan_cols = ['区间手数波动率', '区间买手波动率', '区间卖手波动率', '区间撤VWAP', '区间撤VWAP买', '区间撤VWAP卖', '累计撤VWAP', '累计撤VWAP买', '累计撤VWAP卖', '区间撤比', '累计撤比', '区间撤比手', '累计撤比手', '区间均手', '区间均手买', '区间均手买1', '区间均手买优', '区间均手卖', '区间均手卖1', '区间均手卖优', '累计均手', '累计均手买', '累计均手买1', '累计均手买优', '累计均手卖', '累计均手卖1', '累计均手卖优']
        no_nan_cols = [i for i in list(std_data) if i not in nan_cols]
        assert not std_data.loc[:, no_nan_cols].isnull().values.any() and not np.isinf(std_data.loc[:, no_nan_cols]).values.any()

        # 增加top统计数据
        std_data = self.add_top_n_data(std_data, data)

        # 添加前缀
        std_data.columns = [f'ZBWT_{i}' for i in list(std_data)]

        return std_data

    def raw_name(self):
        """
        返回特征原始文件名称
        """
        return "逐笔委托"

    def name(self):
        """
        返回特征名称
        """
        return "逐笔委托"

    def version(self):
        """
        返回特征版本号
        001_20230911
        """
        return "001_20230911"
