import pandas as pd
import numpy as np

from ..tool import get_time_point, drop_duplicate_row
from ..base_class import zb_base

from ..base.base_data import sdpk_base


def get_rets(price_data, n, method="simple"):
    """
    返回 n 秒后的收益率

    method: simple 简单收益率
            log 对数收益率
    """
    # shift 个数
    n_shift = n//3
    n_price = price_data.shift(-n_shift)

    if method == "simple":
        rets = (n_price - price_data) / price_data

    elif method == "log":
        rets = np.log(n_price / price_data)

    return rets


def get_target(price_data, n=30):
    """
    返回n 秒后标签
    price_data: 标准价格数据, 包含标准的时间索引(3s切片)
    标的概况特征中可以获得
    """
    rets = get_rets(price_data, n)

    # 对收益率进行分类

    return rets


def get_mean_1s_price(data, mean_n_s):
    """计算未来 mean_n_s 秒内的均价（包含当前秒）
    返回dataframe字段:
    amount, vol, mean_price
    """
    # 切片交易时间内的数据
    data = pd.concat([data.between_time("09:30:00", "11:30:00"),
                      data.between_time("13:00:00", "15:00:00")])

    if len(data) == 0:
        return None

    # 添加空行，确保每个切片都有重采样数据
    date_str = str(data.index[0])[:11]
    data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
    data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

    # 计算平均价格
    data['amount'] = data['价格']*data['手']
    data['vol'] = data['手']

    target_data = data.resample(f"1S", closed="right", label="right").apply(
        {
            "amount": np.sum,
            "vol": np.sum,
        }
    )

    # 删除最后一行 第一行
    target_data = target_data[1:-1]

    # 删除 11:30:00 - 13:00:00 之间的数据（不含）
    am = target_data.between_time("09:30:01", "11:30:00")
    pm = target_data.between_time("13:00:01", "15:00:00")
    target_data = target_data.loc[am.index.union(pm.index), :]

    # 计算 mean_n_s 的均价
    # 使用未来 mean_n_s 秒内的价格计算vol权重平均价
    amount = target_data['amount'].values.copy()
    vol = target_data['vol'].values.copy()
    for i in range(1, mean_n_s):
        amount += target_data['amount'].shift(-i).values
        vol += target_data['vol'].shift(-i).values

    # 没有成交部分值处理
    vol = np.where(vol == 0, 1.0, vol)
    amount = np.where(amount == 0, np.nan, amount)
    target_data['mean_price'] = amount / vol
    target_data['mean_price'].fillna(method="ffill", inplace=True)

    return target_data


class target_base_zbcj(zb_base):
    """使用逐笔成交计算的标签基类"""

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def name(self):
        """
        返回特征名称
        """
        return "标签"

    def raw_name(self):
        return "逐笔成交"

    def _get_pkl_path(self, code, date):
        return f'D:/code/featrue_data/data/target_data/{code}/{date}'

    def format_unit(self, data: pd.DataFrame):
        """
        格式化数据单位
        """
        # 时间
        data["时间"] = pd.to_datetime(data["时间"])

        data["手"] = data["手"].astype(float).astype(int)

        data["价格"] = data["价格"].astype(float)

        data = data.set_index("时间")

        return data

    def cal(self, data: pd.DataFrame):
        """
        计算数据
        """
        # 截取需要的列
        data = data.loc[:, ['时间', '序号', '价格', '手']]

        return super().cal(data)


class target_001(target_base_zbcj):
    """
    对价差进行二分类
    使用价格: 时点后 mean_n_s 秒内的均价
    kwargs={
        'mean_n_s': 3,  # 均值计算的秒数区间
        'predict_n_s': 30,  # 预测的秒数
        'diff': 0.002,  # 预测的价差阈值
    }
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "001_20230911"

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        target_data = get_mean_1s_price(data, self.get_kwargs()['mean_n_s'])

        """
        1.上海证券交易所交易规则（2023年修订） :
            3.3.11　A股的申报价格最小变动单位为0.01元人民币，基金、权证交易为0.001元人民币，B股交易为0.001美元。
        
        2.深圳证券交易所交易规则（2023 年修订征求意见稿）:
            3.3.11 A 股交易的申报价格最小变动单位为 0.01 元人民
            币；基金的申报价格最小变动单位为 0.001 元人民币；B 股的申
            报价格最小变动单位为 0.01 港元。
        """
        # 计算标签
        # predict_n_s s后的平均价格价差 >= diff -> 1
        # 否则 -> 0
        predict_n = self.get_kwargs()['predict_n_s']
        diff = self.get_kwargs()['diff']
        target_data['target'] = (target_data['mean_price'].shift(
            -predict_n) - target_data['mean_price']).apply(lambda x: np.nan if np.isnan(x) else 1 if x >= diff else 0)

        # 标准到3s的时间戳
        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        target_data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        target_data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        target_data = target_data.resample(f"3S", closed="right", label="right").apply(
            {
                "target": 'last',
                "mean_price": 'last',
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        return target_data


class target_002(target_base_zbcj):
    """
    使用移动平均线进行分类，以当前时点为中心偏移

    在3秒数据上做移动平均
    kwargs={
        'mean_n_s': 3,  # 均值计算的秒数区间
        'predict_n_s': 30,  # 预测的秒数
        'diff': 0.002,  # 预测的价差阈值
    }
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "002_20231026"

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        mean_n_s = self.get_kwargs()['mean_n_s']
        target_data = get_mean_1s_price(data, mean_n_s)

        # 标准到3s的时间戳
        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        target_data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        target_data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        target_data = target_data.resample(f"3S", closed="right", label="right").apply(
            {
                "mean_price": 'last',
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        ###################################################
        # 计算均线 做两次平滑 矫正延迟
        target_data['mean_price'] = target_data['mean_price'].rolling(
            100).mean().rolling(100).mean().shift(-100)

        # 计算标签
        # predict_n_s s后的平均价格价差 >= diff -> 1
        # 否则 -> 0
        predict_n = self.get_kwargs()['predict_n_s']
        diff = self.get_kwargs()['diff']
        target_data['target'] = target_data['mean_price'].diff(
            predict_n).shift(-predict_n).apply(lambda x: np.nan if np.isnan(x) else 1 if x >= diff else 0)
        ###################################################

        return target_data


class target_003(target_base_zbcj):
    """
    使用移动平均线进行分类，以当前时点为中心偏移
    在1秒数据上做移动平均, 最后3s切片

    kwargs={
        'mean_n_s': 3,  # 均值计算的秒数区间
        'predict_n_s': 30,  # 预测的秒数
        'diff': 0.002,  # 预测的价差阈值
    }
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "003_20231026"

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        mean_n_s = self.get_kwargs()['mean_n_s']
        target_data = get_mean_1s_price(data, mean_n_s)

        ###################################################
        # 计算均线 做两次平滑 矫正延迟
        target_data['mean_price'] = target_data['mean_price'].rolling(
            300).mean().rolling(300).mean().shift(-300)

        # 计算标签
        # predict_n_s s后的平均价格价差 >= diff -> 1
        # 否则 -> 0
        predict_n = self.get_kwargs()['predict_n_s'] * 3
        diff = self.get_kwargs()['diff']
        target_data['target'] = target_data['mean_price'].diff(
            predict_n).shift(-predict_n).apply(lambda x: np.nan if np.isnan(x) else 1 if x >= diff else 0)
        ###################################################

        # 标准到3s的时间戳
        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        target_data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        target_data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        target_data = target_data.resample(f"3S", closed="right", label="right").apply(
            {
                "target": 'last',
                "mean_price": 'last',
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        return target_data


class target_004(target_base_zbcj):
    """
    使用移动平均线进行分类，以当前时点为中心偏移
    在1秒数据上做移动平均, 最后3s切片

    kwargs={
        'n': 600,  # 均值计算周期
        'mean_n_s': 3,  # 均值计算的秒数区间
        'predict_n_s': 30,  # 预测的秒数
        'diff': 0.002,  # 预测的价差阈值
    }
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "004_20231031"

    def to_time_point(self, data: pd.DataFrame):
        """统一成时间切片数据"""
        mean_n_s = self.get_kwargs()['mean_n_s']
        target_data = get_mean_1s_price(data, mean_n_s)

        ###################################################
        # 计算均线 做两次平滑 矫正延迟
        n = self.get_kwargs()['n']
        target_data['mean_price'] = target_data['mean_price'].rolling(
            n).mean().rolling(n).mean().shift(-int(n))

        # 计算标签
        # predict_n_s s后的平均价格价差 >= diff -> 1
        # 否则 -> 0
        predict_n = self.get_kwargs()['predict_n_s'] * 3
        diff = self.get_kwargs()['diff']
        target_data['target'] = target_data['mean_price'].diff(
            predict_n).shift(-predict_n).apply(lambda x: np.nan if np.isnan(x) else 1 if x >= diff else 0)
        ###################################################

        # 标准到3s的时间戳
        # 添加空行，确保每个切片都有重采样数据
        date_str = str(data.index[0])[:11]
        target_data.loc[pd.to_datetime(date_str + "09:29:59")] = np.nan
        target_data.loc[pd.to_datetime(date_str + "15:00:01")] = np.nan

        target_data = target_data.resample(f"3S", closed="right", label="right").apply(
            {
                "target": 'last',
                "mean_price": 'last',
            }
        )

        # 删除最后一行 第一行
        target_data = target_data[1:-1]

        # 删除 11:30:00 - 13:00:00 之间的数据（不含）
        am = target_data.between_time("09:30:01", "11:30:00")
        pm = target_data.between_time("13:00:01", "15:00:00")
        target_data = target_data.loc[am.index.union(pm.index), :]

        return target_data


# 1. 获取原始数据
# 2. 时间列标准化处理 > 2023-10-27 09:15:00
# 3. 调用 self.cal() 计算特征数据
#   3.1. 调用 self.std_format() 标准化数据
#       3.1.1. 根据 self.need_raw_cols() 切片原始数据        << 重写
#       3.1.2. 调用 self.format_unit() 格式化数据单位
#   3.2. 调用after_std_format() 标准化处理后续操作            << 重写
# 4. 调用 self.feature_check() 检查特征数据                  << 重写
class target_005(sdpk_base):
    """
    样本周期 d0
    预测周期 d1
    {'d0':100, 'd1':15}
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "005_20240104"

    def name(self):
        """
        返回特征名称
        """
        return "标签"

    def feature_check(self, data: pd.DataFrame):
        return True

    def need_raw_cols(self):
        return [
            '时间',
            '卖1价',
            '买1价',
        ]

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        计算标签
        """
        # 中间价格
        middle_price = (data['买1价'] + data['卖1价']) / 2

        # 价格变化
        d1 = self.kwargs['d1']
        rets = middle_price.diff(d1).shift(-d1)

        # 标签
        data['target'] = rets.apply(
            lambda x: 1 if x > 0 else -1 if x < 0 else 0 if x == 0 else np.nan)

        return data


class target_006(sdpk_base):
    """
    预测周期 d1
    阈值 a = 0.00001
    平均周期 ma = 200
    {'d1':3, 'a':0.00001, 'ma':200}
    """

    def __init__(self, kwargs) -> None:
        super().__init__(kwargs)

    def version(self):
        return "006_20240115"

    def name(self):
        """
        返回特征名称
        """
        return "标签"

    def feature_check(self, data: pd.DataFrame):
        return True

    def need_raw_cols(self):
        return [
            '卖10价',
            '卖10量',
            '卖9价',
            '卖9量',
            '卖8价',
            '卖8量',
            '卖7价',
            '卖7量',
            '卖6价',
            '卖6量',
            '卖5价',
            '卖5量',
            '卖4价',
            '卖4量',
            '卖3价',
            '卖3量',
            '卖2价',
            '卖2量',
            '卖1价',
            '卖1量',
            '买1价',
            '买1量',
            '买2价',
            '买2量',
            '买3价',
            '买3量',
            '买4价',
            '买4量',
            '买5价',
            '买5量',
            '买6价',
            '买6量',
            '买7价',
            '买7量',
            '买8价',
            '买8量',
            '买9价',
            '买9量',
            '买10价',
            '买10量',
        ]

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        计算标签
        """
        # 删除重复数据
        data = drop_duplicate_row(data)

        # 中间价格
        middle_price = (data['买1价'] + data['卖1价']) / 2

        # 获取参数
        d1 = self.kwargs['d1']
        ma = self.kwargs['ma']
        a = self.kwargs['a']

        data_ma = middle_price.rolling(ma).mean().rolling(ma).mean().shift(-ma)
        rets = data_ma.pct_change(d1).shift(-d1)

        # 标签
        data['target'] = rets.apply(
            lambda x: 1 if x > a else -1 if x < -a else np.nan if np.isnan(x) else 0)

        return data
