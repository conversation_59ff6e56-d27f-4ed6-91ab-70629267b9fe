import os
import shutil
# from py_ext.lzma import listdir_compress_file, decompress
from py_ext.lzma import decompress


class Params:
    kaggle = False


params = Params()

# 路径 
his_root = r"Z:\L2_DATA\his_data" if 'input_raw_folder' not in os.environ else os.environ['input_raw_folder']

# 交易费率
fee_rate = 0.00005
min_fee = 0.01

# 需要的所有code
pre_codes = []


def get_realtime_data_path(date):
    """返回日期对应的实时数据路径 
    date: 20230901
    """
    path = os.path.join(realtime_root + '/ago/data',
                        date) if date < "20230906" else os.path.join(realtime_root, date) + "/data"

    # 检查路径是否存在
    if not os.path.exists(path):
        print(f"实时数据不存在: {path}")

    return path


zip_codes_dict = {}


def get_his_data_path(code, date, filename):
    """
    返回日期对应的盘后历史数据路径 
    code: 513050
    date: 20230901
    """
    # 先检查本地目录
    assert params.kaggle == False
    code_path = os.path.join(his_root, date, code)
    file_path = os.path.join(code_path, filename)
    if not os.path.exists(file_path):
        print(f"本地历史数据不存在: {file_path}")
        return ''

    return file_path


def get_his_times_binance():
    """返回所有的历史数据毫秒时间戳"""
    # 1708434355772 毫秒时间戳列表
    times_net = [i.replace("trade_", '') for i in os.listdir(
        his_binance_root) if "trade_" in i]

    times = list(set(times_net))
    times.sort()
    return times


def get_his_dates():
    """返回所有的历史数据日期"""
    raw_folder = his_root
    all_dates = [i.split('_')[0]
                 for i in os.listdir(raw_folder) if len(i) == 8]
    all_dates = list(set(all_dates))
    all_dates = sorted(all_dates)

    return all_dates


def get_realtime_dates():
    """返回所有的实时录制数据日期"""
    # 8位 且全是数字
    return [i for i in os.listdir(realtime_root + '/ago/data') + os.listdir(realtime_root) if len(i) == 8 and i.isdigit()]


if __name__ == "__main__":
    his_dates = get_his_dates()
    realtime_dates = get_realtime_dates()
    print(his_dates)
    print(realtime_dates)
