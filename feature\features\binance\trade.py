from abc import ABC, abstractmethod

import pandas as pd
import numpy as np

from ...tool import drop_duplicate_row
from ...base_class import mongo_base


class trade_base(mongo_base):
    def __init__(self, kwargs: dict = {}) -> None:
        super().__init__(kwargs)

    def name(self):
        """
        返回特征名称
        """
        return "trade"

    def need_cols(self):
        return [
            "event_timestamp",
            "save_timestamp",
            "symbol",
            "price",
            "vol",
            "id",
            "buy_id",
            "sell_id",
            "deal_timestamp",
            "is_buyer_maker"]

    @abstractmethod
    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        """
        return data

    def get(self, code, mongo_client, _begin_timestamp, _end_timestamp):
        """
        获取指定标的的深度数据
        """
        filter = {}
        for i in self.need_cols():
            filter[i] = 1

        if 'save_timestamp' not in filter:
            filter['save_timestamp'] = 1

        if "id" not in filter:
            filter["id"] = 1

        # 获取数据
        data = mongo_client['binance']['trade'].find(
            {'symbol': code.lower(), 'save_timestamp': {
                '$gte': _begin_timestamp * 1000, '$lt': _end_timestamp * 1000}}, filter
        )
        data = pd.DataFrame(list(data))
        if len(data) == 0:
            return None

        # 去重
        data = drop_duplicate_row(data)

        # 删除id重复
        data.drop_duplicates(subset='id', keep='first', inplace=True)

        # 格式化时间
        data['deal_timestamp'] = pd.to_datetime(
            data['deal_timestamp'], origin='1970-01-01 08:00:00', unit='ms')

        return self.after_std_format(data)


class trade_001(trade_base):
    def __init__(self, kwargs: dict = {}) -> None:
        """
        """
        super().__init__(kwargs)

    def after_std_format(self, data: pd.DataFrame):
        """
        标准化处理后续操作
        储存时间        | 平均买单成交价格  平均卖单成交价格    买单成交量  卖单成交量  买单成交笔数    卖单成交笔数
        save_timestamp  | avg_buy_price     avg_sell_price      buy_vol     sell_vol    buy_count       sell_count

        不在此处实现，需要统计时间范围
        """

        # 返回列
        # symbol, price, vol, deal_timestamp, is_buyer_maker

        return data.loc[:, ['price', 'vol', 'is_buyer_maker', 'id', 'deal_timestamp']]
